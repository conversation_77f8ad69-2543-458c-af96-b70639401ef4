component extends="handlers.BaseHandler" {
	
	property name="referralService" inject="ReferralService@mcapiV1";
	property name="utils" inject="Utilities@mcapiV1";
	
	this.allowedMethods = getAllowedMethodsForHandler(handler=listLast(GetComponentMetaData(this).name,'.'));

	// (GET) /v1/referral
	any function index (event, rc, prc) {
		var strPayload = utils.getBodyJSON();

		if (NOT isStruct(strPayload) AND len(strPayload)) {
			prc.oef_message = "Invalid JSON object in the body.";
			onExpectationFailed(event,rc,prc);
		} else {
			if (NOT isStruct(strPayload))
				strPayload = { count=10, start=0, search={} };
			if (!structKeyExists(strPayload,"count"))
				strPayload.insert("count",10);
			if (!structKeyExists(strPayload,"start"))
				strPayload.insert("start",0);
			if (!structKeyExists(strPayload,"search"))
				strPayload.insert("search",{});

			if (strPayload.count() != 3) {
				prc.oef_message = "Invalid keys in JSON object in the body.";
				onExpectationFailed(event,rc,prc);
			} elseif (NOT isNumeric(strPayload.count)) {
				prc.oef_message = "Invalid count in JSON object.";
				onExpectationFailed(event,rc,prc);
			} elseif (NOT isNumeric(strPayload.start)) {
				prc.oef_message = "Invalid start in JSON object.";
				onExpectationFailed(event,rc,prc);
			} elseif (NOT isStruct(strPayload.search)) {
				prc.oef_message = "Invalid search in JSON object.";
				onExpectationFailed(event,rc,prc);
			} else {
				prc.response.setData(referralService.getReferrals(orgID=prc.token.mco, siteID=prc.token.mcs, count=strPayload.count, start=strPayload.start, strPayloadSearch=strPayload.search));
			}
		}
	}

	// (GET) /v1/referral/{api_id}
	any function view (event, rc, prc) {
		var strReferral = referralService.getReferral(orgID=prc.token.mco, siteID=prc.token.mcs, clientReferralUID=urldecode(rc.api_id));
		if (strReferral.clientReferralID == 0 or strReferral.count == 0)
			prc.response.setError(true).addMessage("Referral not found.").setStatusCode(STATUS.NOT_FOUND).setStatusText("REFERRAL NOT FOUND");
		else
			prc.response.setData( { referral=strReferral.referral, count=strReferral.count });
	}

	// (PUT) /v1/referral/{api_id}
	any function update (event, rc, prc) {
		var strPayload = utils.getBodyJSON();
		
		if (NOT isStruct(strPayload)) {
			prc.oef_message = "Invalid or missing JSON object in the body.";
			onExpectationFailed(event,rc,prc);
		} elseif (strPayload.count() == 0) {
			prc.oef_message = "Invalid or empty JSON object in the body.";
			onExpectationFailed(event,rc,prc);
		} else {
			var recordedbymemberid = prc.token.mcm;
			if (structKeyExists(strPayload,"_mcrecordedbymemberid")) {
				if (isValid("integer",strPayload._mcrecordedbymemberid)) 
					recordedbymemberid = strPayload._mcrecordedbymemberid;
				strPayload.delete("_mcrecordedbymemberid");
			}

			var strUpdateResult = referralService.updateReferral(orgID=prc.token.mco, siteID=prc.token.mcs, clientReferralUID=urldecode(rc.api_id), referralinfo=strPayload, recordedbymemberid=recordedbymemberid);
			if (strUpdateResult.clientReferralID == 0) {
				prc.response.setError(true).addMessage("Referral not found.").setStatusCode(STATUS.NOT_FOUND).setStatusText("REFERRAL NOT FOUND");
			} 
			elseif (structKeyExists(strUpdateResult, "error") and isArray(strUpdateResult.error) and arraylen(strUpdateResult.error)) {
				strUpdateResult.error.prepend("Unable to update referral.");
				prc.response.setError(true).addMessage(strUpdateResult.error).setStatusCode(STATUS.NOT_ACCEPTABLE).setStatusText("NOT ACCEPTABLE");
			}
			elseif (strUpdateResult.count < 1){
				prc.response.setError(true).addMessage("Unable to update referral.").setStatusCode(STATUS.INTERNAL_ERROR).setStatusText("NOT UPDATED");
			}
			else {
				prc.response.setData({ result="Referral updated.", referral=strUpdateResult.referral, count=strUpdateResult.count, ignoredfields=strUpdateResult.ignoredfields }).setStatusCode(STATUS.SUCCESS).setStatusText("UPDATED");
			}
		}
	}

	// (DELETE) /v1/referral/{api_id}
	any function delete (event, rc, prc) {
		var strPayload = utils.getBodyJSON();

		var recordedbymemberid = prc.token.mcm;
		if (isStruct(strPayload) and structKeyExists(strPayload,"_mcrecordedbymemberid") and isValid("integer",strPayload._mcrecordedbymemberid)) 
			recordedbymemberid = strPayload._mcrecordedbymemberid;

		var deleteStatus = referralService.deleteReferral(orgID=prc.token.mco, siteID=prc.token.mcs, clientReferralUID=urldecode(rc.api_id), recordedbymemberid=recordedbymemberid);
		if (deleteStatus == 'referralNotFound')
			prc.response.setError(true).addMessage("Referral not found.").setStatusCode(STATUS.NOT_FOUND).setStatusText("REFERRAL NOT FOUND");
		elseif (deleteStatus == 'invalidReferral')
			prc.response.setError(true).addMessage("Referral cannot be deleted.").setStatusCode(STATUS.NOT_ACCEPTABLE).setStatusText("NOT ACCEPTABLE");
		elseif (deleteStatus != 'ok')
			prc.response.setError(true).addMessage("Unable to delete referral.").setStatusCode(STATUS.INTERNAL_ERROR).setStatusText("NOT UPDATED");
		else
			prc.response.setData({ "result"="Referral deleted." });
	}

}

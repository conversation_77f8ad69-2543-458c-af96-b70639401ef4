<div class="get method-example">
	<div class="method-wrapper">
		<div class="method">GET</div>
		<div class="method-text">
			<div style="float:left;">/v1/referral/{api_id}</div>
			<div style="float:right;"><i class="fa fa-code fa-border" style="font-size:14px;"></i></div>
		</div>
	</div>
</div>
<div class="method-exampleCode" style="display:none;">
	<div class="jsonblock-head">Sample Request</div>
	<div class="jsonblock">
<pre class="prettyprint">
GET /v1/referral/XXXXXXXX-XXXX-XXXX-XXXX-XXXXXXXXXXXX HTTP/1.1
Authorization: Bearer YOUR_API_TOKEN
Host: api.membercentral.com
</pre>
	</div>
	<div class="jsonblock-head">Possible Response Codes</div>
	<div class="jsonblock-table">
		<table>
		<tr><td class="rc">200 OK</td><td>success</td></tr>
		<tr><td class="rc">401 INVALID TOKEN</td><td>invalid authorization token</td></tr>
		<tr><td class="rc">403 EXPIRED TOKEN</td><td>authorization token expired</td></tr>
		<tr><td class="rc">404 REFERRAL NOT FOUND</td><td>invalid referral api_id</td></tr>
		</table>
	</div>
	<div class="jsonblock-head">Sample Response - Not Referred (success)</div>
	<div class="jsonblock">
<pre class="prettyprint">
200 OK

{
    "data": {
        "count": 1,
        "referral": {
            "calldate": "1/1/2024 11:04 AM",
            "lastupdateddate": "3/1/2024 4:16 PM",
            "referraldate": "1/4/2024 11:08 AM",
            "caseopendate": "2/1/2024 2:00 PM",
            "casecloseddate": "3/1/2024 4:16 PM",
            "source": "Web / Internet",
            "language": "English",
            "legalissue": "Family Law: Divorce With Children involved",
            "agreesurvey": 1,
            "agreenewsletter": 0,
            "isreferred": 0,
            "caseexists": 0,
            "client": {
                "clientfirstname": "Sally",
                "clientmiddlename": "",
                "clientlastname": "Smith",
                "clientcompany": "",
                "clientemail": "<EMAIL>",
                "clientaddress1": "123 AnyStreet",
                "clientaddress2": "",
                "clientcity": "Anytown",
                "clientstateprov": "Texas",
                "clientpostalcode": "78735",
                "clientphonehome": "",
                "clientphonecell": "************",
                "clientphonealternate": ""
            },
            "representative": {
                "repfirstname": "",
                "replastname": "",
                "repaddress1": "",
                "repaddress2": "",
                "repcity": "",
                "repstateprov": "",
                "reppostalcode": "",
                "repemail": "",
                "repphonehome": "",
                "repphonecell": "",
                "repphonealternate": "",
                "relationtoclient": ""
            },
            "counselor": {
                "counselorfirstname": "Tammy",
                "counselorlastname": "Thompson",
                "counselormembernumber": "ABC123456"
            },
            "call": {
                "calltype": "Lawyer Referral",
                "callid": "XXXXXXXX-XXXX-XXXX-XXXXXXXXXXXXXXXX"
            },
            "clientfeesinfo": {
                "clientfees": [
                    {
                        "date": "9/11/2024",
                        "fee": "8.00",
                        "amounttobepaid": "0.00",
                        "paidtodate": "8.00"
                    }
                ],
                "clientfeestotals": {
                    "fee": "8.00",
                    "amounttobepaid": "0.00",
                    "paidtodate": "8.00"
                }
            },
            "counselornotes": [
                {
                    "enteredby": "John Doe",
                    "createddate": "09/26/2024 12:00 AM",
                    "followupstatus": "Completed",
                    "followupdate": "10/26/2024",
                    "description": "This is a test counselor note."
                },
                ...
            ],
            "agencyname": "Legal Shield",
            "referralstatus": "Pending - Referral NOT sent",
            "filters": {
                "primarypanel": "Criminal Law",
                "primarysubpanel": "Bank Robbery|Tax Evasion",
                "secondarypanel": "",
                "secondarysubpanel": "N/A",
                "tertiarypanel": "",
                "tertiarysubpanel": "N/A",
                "customfields": [
                    {
                        "value": "John",
                        "label": "First Name"
                    },
                    {
                        "value": "01/01/1988",
                        "label": "Date Of Birth"
                    },
                    {
                        "value": "English|German|French",
                        "label": "Languages"
                    },
					...
                ],
                "classifications": [
                    {
                        "value": "Board Members",
                        "label": "Classification"
                    },
                    {
                        "value": "Less than 5 Years in Practice|Over 10 Years in Practice",
                        "label": "Membership Level"
                    },
					...
                ]
            },
            "questionanswerpath": "Select a Panel / Administrative / Yes or No / Yes /",
            "clientcustomfields": {
                "fieldgroupings": [
                    {
                        "ungrouped": 1,
                        "fields": [
                            {
                                "field_api_id": "XXXXXXXX-XXXX-XXXX-XXXX-XXXXXXXXXXXX",
                                "value": "Attorney referred me",
                                "label": "How did you hear about us?"
                            },
                            ...
                        ],
                        "groupname": ""
                    },
                    {
                        "ungrouped": 0,
                        "fields": [
                            {
                                "field_api_id": "XXXXXXXX-XXXX-XXXX-XXXX-XXXXXXXXXXXX",
                                "value": "No",
                                "label": "Do you need special accommodations?"
                            },
                            ...
                        ],
                        "groupname": "Optional Information"
                    },
                    ...
                ]
            },
            "x-delete-api-uri": "/v1/referral/XXXXXXXX-XXXX-XXXX-XXXX-XXXXXXXXXXXX"
        }
    },
    "error":false,
    "messages":[]
}
</pre>
	</div>
	<div class="jsonblock-head">Sample Response - Referred (success)</div>
	<div class="jsonblock">
<pre class="prettyprint">
200 OK

{
    "data": {
        "count": 1,
        "referral": {
            "calldate": "1/1/2024 11:04 AM",
            "lastupdateddate": "3/1/2024 4:16 PM",
            "referraldate": "1/4/2024 11:08 AM",
            "caseopendate": "2/1/2024 2:00 PM",
            "casecloseddate": "3/1/2024 4:16 PM",
            "source": "Web / Internet",
            "language": "English",
            "legalissue": "Family Law: Divorce With Children involved",
            "agreesurvey": 1,
            "agreenewsletter": 0,
            "isreferred": 1,
            "caseexists": 1,
            "client": {
                "clientfirstname": "Sally",
                "clientmiddlename": "",
                "clientlastname": "Smith",
                "clientcompany": "",
                "clientemail": "<EMAIL>",
                "clientaddress1": "123 AnyStreet",
                "clientaddress2": "",
                "clientcity": "Anytown",
                "clientstateprov": "Texas",
                "clientpostalcode": "78735",
                "clientphonehome": "",
                "clientphonecell": "************",
                "clientphonealternate": ""
            },
            "representative": {
                "repfirstname": "",
                "replastname": "",
                "repaddress1": "",
                "repaddress2": "",
                "repcity": "",
                "repstateprov": "",
                "reppostalcode": "",
                "repemail": "",
                "repphonehome": "",
                "repphonecell": "",
                "repphonealternate": "",
                "relationtoclient": ""
            },
            "counselor": {
                "counselorfirstname": "Tammy",
                "counselorlastname": "Thompson",
                "counselormembernumber": "ABC123456"
            },
            "call": {
                "calltype": "Lawyer Referral",
                "callid": "XXXXXXXX-XXXX-XXXX-XXXXXXXXXXXXXXXX"
            },
            "lawyer": {
                "lawyerfirstname": "J",
                "lawyerlastname": "Williams",
                "lawyermembernumber": "DEF987654"
            },
            "lawyernotes": [
                {
                    "enteredby": "John Doe",
                    "createddate": "09/26/2024 12:00 AM",
                    "followupstatus": "Completed",
                    "followupdate": "10/26/2024",
                    "description": "This is a test lawyer note."
                },
                ...
            ],
            "feetype": "Consultation",
            "case": {
                "feediscrepanciesstatuslogs": [
                    {
                        "date": "10/01/2024",
                        "notes": "Not Resolved",
                        "enteredby": "John Doe",
                        "status": "No Resolution Reached"
                    },
                    ...
                ],
                "feesreportedbyclient": "0.00"
                "attorney": {
                    "attorneymembernumber": "ZZ20110628170200",
                    "attorneyfirstname": "J",
                    "attorneylastname": "Williams"
                },
                "casefeesinfo": {
                    "casefeestotals": {
                        "filingfee": "0.00",
                        "collectedfee": "11,500.00",
                        "referraldues": "30.00",
                        "amounttobepaid": "0.00"
                    },
                    "casefees": [
                        {
                            "filingfee": "0.00",
                            "collectedfee": "1,000.00",
                            "referraldues": "10.00",
                            "amounttobepaid": "0.00",
                            "collectedfeedate": "9/23/2024"
                        },
                        ...
                    ]
                },
                "status": "Closed - Final Payment"
            }
            "clientfeesinfo": {
                "clientfees": [
                    {
                        "date": "9/11/2024",
                        "fee": "8.00",
                        "amounttobepaid": "0.00",
                        "paidtodate": "8.00"
                    }
                ],
                "clientfeestotals": {
                    "fee": "8.00",
                    "amounttobepaid": "0.00",
                    "paidtodate": "8.00"
                }
            },
            "consultationfeesinfo": {
                "consultationfeestotals": {
                    "fee": "25.00",
                    "amounttobepaid": "0.00",
                    "paidtodate": "25.00"
                },
                "consultationfees": [
                    {
                        "date": "9/12/2024",
                        "fee": "25.00",
                        "amounttobepaid": "0.00",
                        "paidtodate": "25.00"
                    }
                ]
            },
            "counselornotes": [
                {
                    "enteredby": "John Doe",
                    "createddate": "09/26/2024 12:00 AM",
                    "followupstatus": "Completed",
                    "followupdate": "10/26/2024",
                    "description": "This is a test counselor note."
                },
                ...
            ],
            "agencyname": "Legal Shield",
            "filters": {
                "primarypanel": "Criminal Law",
                "primarysubpanel": "Bank Robbery|Tax Evasion",
                "secondarypanel": "",
                "secondarysubpanel": "N/A",
                "tertiarypanel": "",
                "tertiarysubpanel": "N/A",
                "customfields": [
                    {
                        "value": "John",
                        "label": "First Name"
                    },
                    {
                        "value": "01/01/1988",
                        "label": "Date Of Birth"
                    },
                    {
                        "value": "English|German|French",
                        "label": "Languages"
                    },
                    {
                        "value": "Within 5 miles of Postal Code 12345",
                        "label": "Postal Code"
                    }
                ],
                "classifications": [
                    {
                        "value": "Board Members",
                        "label": "Classification"
                    },
                    {
                        "value": "Less than 5 Years in Practice|Over 10 Years in Practice",
                        "label": "Membership Level"
                    }
                ]
            },
            "attorneycustomfields": {
                "fieldgroupings": [
                    {
                        "ungrouped": 0,
                        "fields": [
                            {
                                "field_api_id": "XXXXXXXX-XXXX-XXXX-XXXX-XXXXXXXXXXXX",
                                "value": "What is the best way to reach you?",
                                "label": "Email"
                            },
                            ...
                        ],
                        "groupname": "Contact Information"
                    },
                    ...
                ]
            },
            "questionanswerpath": "Select a Panel / Administrative / Yes or No / Yes /",
            "clientcustomfields": {
                "fieldgroupings": [
                    {
                        "ungrouped": 1,
                        "fields": [
                            {
                                "field_api_id": "XXXXXXXX-XXXX-XXXX-XXXX-XXXXXXXXXXXX",
                                "value": "Attorney referred me",
                                "label": "How did you hear about us?"
                            },
                            ...
                        ],
                        "groupname": ""
                    },
                    {
                        "ungrouped": 0,
                        "fields": [
                            {
                                "field_api_id": "XXXXXXXX-XXXX-XXXX-XXXX-XXXXXXXXXXXX",
                                "value": "No",
                                "label": "Do you need special accommodations?"
                            },
                            ...
                        ],
                        "groupname": "Optional Information"
                    },
                    ...
                ]
            }
        }
    },
    "error":false,
    "messages":[]
}
</pre>
	</div>
	<div class="jsonblock-head">Sample Response (failure)</div>
	<div class="jsonblock">
<pre class="prettyprint">
404 REFERRAL NOT FOUND

{
    "data": {},
    "error": true,
    "messages": [
        "Referral not found."
    ]
}
</pre>
	</div>
</div>
<div style="clear:both;padding-top:10px;"></div>
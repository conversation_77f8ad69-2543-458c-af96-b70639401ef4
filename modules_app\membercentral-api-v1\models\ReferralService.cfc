component cache="true" {

	property name="referralStatusService" inject="ReferralStatusService@mcapiV1";
	property name="regex" type="struct" inject="coldbox:setting:regEx@membercentral-api-v1";
	property name="environment" type="string" inject="coldbox:setting:environment";
	
	struct function getReferrals (required numeric orgID, required numeric siteID, required numeric count, required numeric start, required struct strPayloadSearch) {
		var referralID = getReferralID(siteID=arguments.siteID);
		var arrPayloadSearchIgnored = [];

		if (referralID > 0) {
			var stFilterClause = "";
			var stJoinClause = "";
			var cfsqltype = "";
			var keyValue = "";

			var sqlParams = {
				orgID = { value=arguments.orgID, cfsqltype="cf_sql_integer" },
				siteID = { value=arguments.siteID, cfsqltype="cf_sql_integer" },
				count = { value=arguments.count, cfsqltype="cf_sql_integer" },
				start = { value=arguments.start, cfsqltype="cf_sql_integer" },
				environment = { value=environment, cfsqltype="cf_sql_varchar" },
				referralID = { value=referralID, cfsqltype="cf_sql_integer" }
			};

			// get possible referral status names into a new array
			var arrReferralStatuses = referralStatusService.getStatuses(siteID=arguments.siteID).referralstatus.map(function(item){
				return arguments.item.statusname;		
			},true);

			// translate json into where clause
			// verify keys are simple values and not null
			var arrFields = [
				"clientfirstname","clientlastname","clientphone","clientemail","repfirstname","replastname","lawyerfirstname","lawyerlastname","status",
				"counselorfirstname","counselorlastname","refdatefrom","refdateto","calldatefrom","calldateto","notefollowupdatefrom","notefollowupdateto",
				"notefollowupstatus","referralnum","calluid","feediscrepancystatus","istransferred","transferdatefrom","transferdateto","hasdocuments"
			];

			for (var key in arguments.strPayloadSearch) {
				if (!arrFields.findNoCase(key) or !structKeyExists(arguments.strPayloadSearch,key) or !isSimpleValue(arguments.strPayloadSearch[key])) {
					arrPayloadSearchIgnored.append(key);
					arguments.strPayloadSearch.delete(key);
				} else if (len(trim(arguments.strPayloadSearch[key]))) {
					keyvalue = trim(arguments.strPayloadSearch[key]);

					if (key == "notefollowupstatus" and !listFindNoCase("pending,completed",keyvalue))
						return { "error":"Invalid #key#" };
					else if (listFindNoCase("istransferred,hasdocuments",key) and !listFindNoCase("0,1,true,false",keyvalue))
						return { "error":"Invalid #key#" };
					else if (key == "status" and !arrReferralStatuses.findNoCase(keyvalue))
						return { "error":"Invalid #key#" };
					else if (listFindNoCase("refdatefrom,refdateto,calldatefrom,calldateto,notefollowupdatefrom,notefollowupdateto,transferdatefrom,transferdateto",key) and !isDate(keyvalue))
						return { "error":"Invalid #key#" };

					if (listFindNoCase("refdatefrom,refdateto,calldatefrom,calldateto,notefollowupdatefrom,notefollowupdateto,transferdatefrom,transferdateto",key)) cfsqltype = "CF_SQL_DATE";
					else if (listFindNoCase("istransferred,hasdocuments",key)) cfsqltype = "CF_SQL_BIT";
					else if (key == "referralnum") cfsqltype = "CF_SQL_INTEGER";
					else cfsqltype = "CF_SQL_VARCHAR";

					switch (key) {
						case "clientfirstname":
							stFilterClause &= "and c.firstName = :clientfirstname ";
							break;
						case "clientlastname":
							stFilterClause &= "and c.lastName = :clientlastname ";
							break;
						case "clientphone":
							stFilterClause &= "and c.phoneForSearch = :clientphone ";
							break;
						case "clientemail":
							stFilterClause &= "and c.email = :clientemail ";
							break;
						case "lawyerfirstname":
							stFilterClause &= "and mRef.firstName = :lawyerfirstname ";
							break;
						case "lawyerlastname":
							stFilterClause &= "and mRef.lastName = :lawyerlastname ";
							break;
						case "status":
							stFilterClause &= "and crs.statusName = :status ";
							break;
						case "refdatefrom":
							stFilterClause &= "and cr.clientReferralDate >= :refdatefrom ";
							break;
						case "refdateto":
							stFilterClause &= "and cr.clientReferralDate < :refdateto ";
							break;
						case "calldatefrom":
							stFilterClause &= "and cr.dateCreated >= :calldatefrom ";
							break;
						case "calldateto":
							stFilterClause &= "and cr.dateCreated < :calldateto ";
							break;
						case "referralnum":
							stFilterClause &= "and cr.clientReferralID = :referralnum ";
							break;
						case "calluid":
							stFilterClause &= "and cr.callUID = :calluid ";
							break;
						case "feediscrepancystatus":
							stJoinClause &= "inner join dbo.ref_feeDiscrepancyStatuses as fds on fds.feeDiscrepancyStatusID = cr.feeDiscrepancyStatusID and fds.statusName = :feediscrepancystatus ";
							break;
						case "istransferred":
							if (listFindNoCase("1,true",keyvalue))
								stFilterClause &= "and exists ";
							else
								stFilterClause &= "and not exists ";
							stFilterClause &= "(select top 1 transferHistoryID from dbo.ref_transferHistory where clientReferralID = cr.clientReferralID and referralID = @referralID) ";
							break;
						case "hasdocuments":
							if (listFindNoCase("1,true",keyvalue))
								stFilterClause &= "and exists ";
							else
								stFilterClause &= "and not exists ";
							stFilterClause &= "(select top 1 caseDocumentID from dbo.ref_caseDocuments where clientreferralID = cr.clientReferralID) ";
							break;
					}

					if (listFindNoCase("refdateto,calldateto,notefollowupdateto,transferdateto",key)) {
						structInsert(sqlParams,key,{ value="#dateAdd("d", 1, keyvalue)#", cfsqltype="#cfsqltype#" });
					} else if (key eq "clientphone") {
						structInsert(sqlParams,key,{ value="#ReReplaceNoCase(keyvalue,'[^0-9]','','ALL')#", cfsqltype="#cfsqltype#" });
					} else {
						structInsert(sqlParams,key,{ value="#keyvalue#", cfsqltype="#cfsqltype#" });
					}
				} else {
					arguments.strPayloadSearch.delete(key);
				}
			}

			if (sqlParams.keyExists("repfirstname") or sqlParams.keyExists("replastname")) {
				stJoinClause &= "inner join dbo.ref_clients as rep on rep.referralID = @referralID and rep.clientID = cr.representativeID "
				if (sqlParams.keyExists("repfirstname"))
					stJoinClause &= "and rep.firstName = :repfirstname ";
				if (sqlParams.keyExists("replastname"))
					stJoinClause &= "and rep.lastName = :replastname ";
			}

			if (sqlParams.keyExists("counselorfirstname") or sqlParams.keyExists("counselorlastname")) {
				stJoinClause &= "inner join dbo.ams_members as counselorM on counselorM.memberID = cr.enteredByMemberID and counselorM.orgID = @orgID ";
				stJoinClause &= "inner join dbo.ams_members as counselorM2 on counselorM2.memberID = counselorM.activeMemberID and counselorM2.orgID = @orgID ";
				if (sqlParams.keyExists("counselorfirstname"))
					stJoinClause &= "and counselorM2.firstName = :counselorfirstname ";
				if (sqlParams.keyExists("counselorlastname"))
					stJoinClause &= "and counselorM2.lastName = :counselorlastname ";
			}

			if (sqlParams.keyExists("notefollowupstatus") or sqlParams.keyExists("notefollowupdatefrom") or sqlParams.keyExists("notefollowupdateto")) {
				stFilterClause &= "and cr.clientreferralID in (";
				stFilterClause &= "select clientreferralID from dbo.ref_notes as rn where rn.clientreferralID = cr.clientreferralID and rn.noteType = 'C' ";
				if (sqlParams.keyExists("notefollowupstatus"))
					stFilterClause &= "and rn.followUpStatus = CASE :notefollowupstatus WHEN 'Pending' THEN 'P' WHEN 'Completed' THEN 'C' ELSE :notefollowupstatus END ";
				if (sqlParams.keyExists("notefollowupdatefrom"))
					stFilterClause &= "and rn.followUpDate >= :notefollowupdatefrom ";
				if (sqlParams.keyExists("notefollowupdateto"))
					stFilterClause &= "and rn.followUpDate < :notefollowupdateto ";
				stFilterClause &= ") ";
			}

			if (sqlParams.keyExists("istransferred") and (sqlParams.keyExists("transferdatefrom") or sqlParams.keyExists("transferdateto"))) {
				if (sqlParams.keyExists("transferdatefrom"))
					stFilterClause &= "and th.transferDt >= :transferdatefrom ";
				if (sqlParams.keyExists("transferdateto"))
					stFilterClause &= "and th.transferDt < :transferdateto ";
			}

			var arrResult = queryExecute("
				SET NOCOUNT ON;
				SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

				DECLARE @orgID int = :orgID, @siteID int = :siteID, @referralID int = :referralID, @environmentID int,
					@environmentName varchar(50) = :environment, @mainHostname varchar(80), @scheme varchar(10);
				SELECT @environmentID = environmentID FROM dbo.platform_environments WHERE environmentName = @environmentName;
				
				SELECT @mainHostname = sh.hostname, @scheme = CASE WHEN sh.hasssl = 1 THEN 'https' ELSE 'http' END
				FROM dbo.siteHostnames AS sh 
				INNER JOIN dbo.siteEnvironments AS se ON se.siteID = sh.siteID
					AND se.environmentID = @environmentID
					AND se.mainHostnameID = sh.hostNameID
					AND sh.siteID = @siteID;

				IF OBJECT_ID('tempdb..##tmpReferrals') IS NOT NULL
					DROP TABLE ##tmpReferrals;
				CREATE TABLE ##tmpReferrals (clientreferralid int PRIMARY KEY, [uid] uniqueidentifier, clientfirstname varchar(75),
					clientlastname varchar(75), membernumber varchar(50), isdeleted bit, islawyerreferral bit, lawyername varchar(255),
					clientreferraldate datetime, [status] varchar(255));

				INSERT INTO ##tmpReferrals (clientreferralid, [uid], clientfirstname, clientlastname, membernumber, isdeleted,
					islawyerreferral, lawyername, clientreferraldate, [status])
				SELECT DISTINCT cr.clientReferralID, cr.[uid], c.firstName, c.lastName, mRef2.memberNumber, crs.isDeleted, crt.isReferral, 
					case when crt.isReferral = 1 then mRef2.firstName + ' ' + mRef2.lastName else a.name end as lawyerName,
					cr.clientReferralDate, crs.statusName
				FROM dbo.ref_clients AS c
				INNER JOIN dbo.ref_clientTypes AS ct ON ct.clientTypeID = c.typeID AND ct.clientType = 'Client'
				INNER JOIN dbo.ref_clientReferrals AS cr ON cr.referralID = @referralID AND cr.clientID = c.clientID
				INNER JOIN dbo.ref_clientReferralStatus AS crs ON crs.clientReferralStatusID = cr.statusID
				LEFT OUTER JOIN dbo.ref_clientReferralTypes AS crt ON crt.clientReferralTypeID = cr.typeID
				LEFT OUTER JOIN dbo.ams_members as mRef 
					INNER JOIN dbo.ams_members as mRef2 on mRef2.orgID = @orgID and mref2.memberID = mRef.activeMemberID
					on mRef.orgID = @orgID and mRef.memberID = cr.memberID
				LEFT OUTER JOIN dbo.ref_agencies AS a ON a.referralID = @referralID AND a.agencyID = cr.agencyID
				#stJoinClause#
				WHERE c.referralID = @referralID
				#stFilterClause#;

				SELECT clientreferralid as referralnum, clientfirstname, clientlastname, membernumber, isdeleted,
					islawyerreferral, lawyername, convert(varchar(20),clientreferraldate,101) as calldate, 
					[status], [uid] as api_id,
					'/v1/referral/' + cast([uid] as varchar(36)) as [x-api-uri],
					@scheme + '://'+ @mainHostname + '/?pg=admin&jumpToTool=ReferralsAdmin%7ClistReferrals%7CeditClient&clientReferralID=' + cast(clientreferralid AS varchar(20)) AS [x-referraladmin-uri]
				FROM ##tmpReferrals
				ORDER BY clientreferralid
				OFFSET :start ROWS FETCH NEXT :count ROWS ONLY;

				IF OBJECT_ID('tempdb..##tmpReferrals') IS NOT NULL
					DROP TABLE ##tmpReferrals;

				SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
				", 
				sqlParams, { datasource="membercentral", returntype="array" }
			);

		} else 
			var arrResult = [];

		if (arrPayloadSearchIgnored.len() > 0)
			return { referrals=arrResult, count=arrayLen(arrResult), ignoredfields=arrPayloadSearchIgnored };
		else
			return { referrals=arrResult, count=arrayLen(arrResult) };
	}

	struct function getReferral (required numeric orgID, required numeric siteID, required string clientReferralUID) {
		var strResult = { referral=structNew("ordered"), clientReferralID=0, count=0 };

		if (len(trim(arguments.clientReferralUID)) and isValid("GUID",arguments.clientReferralUID)) {
			var referralID = getReferralID(siteID=arguments.siteID);
			strResult.clientReferralID = getClientReferralIDByUID(referralID=referralID, clientReferralUID=arguments.clientReferralUID);

			if (strResult.clientReferralID > 0) {
				var qryResult = getReferralData(orgID=arguments.orgID, siteID=arguments.siteID, referralID=referralID, clientReferralID=strResult.clientReferralID);

				if (arraylen(qryResult) == 1) {
					structInsert(strResult.referral,"calldate",dateTimeFormat(qryResult[1].dateCreated,"m/d/yyyy h:nn tt"));
					structInsert(strResult.referral,"lastupdateddate",dateTimeFormat(qryResult[1].dateLastUpdated,"m/d/yyyy h:nn tt"));
					structInsert(strResult.referral,"referraldate",dateTimeFormat(qryResult[1].clientReferralDate,"m/d/yyyy h:nn tt"));
					structInsert(strResult.referral,"caseopendate",dateTimeFormat(qryResult[1].dateCaseOpened,"m/d/yyyy h:nn tt"));
					structInsert(strResult.referral,"casecloseddate",dateTimeFormat(qryResult[1].datecaseclosed,"m/d/yyyy h:nn tt"));
					structInsert(strResult.referral,"source",qryResult[1].clientReferralSource);
					if(qryResult[1].clientReferralSource eq "Other")
						structInsert(strResult.referral,"othersource",qryResult[1].otherSource);
					structInsert(strResult.referral,"language",qryResult[1].languageName);
					structInsert(strResult.referral,"legalissue",qryResult[1].issueDesc);
					structInsert(strResult.referral,"agreesurvey",qryResult[1].sendSurvey);
					structInsert(strResult.referral,"agreenewsletter",qryResult[1].sendNewsBlog);
					structInsert(strResult.referral,"isreferred",val(qryResult[1].isReferred));
					structInsert(strResult.referral,"caseexists",val(qryResult[1].caseID) gt 0 ? 1 : 0);

					strResult.referral.client = {
						clientfirstname: qryResult[1].firstName,
						clientmiddlename: qryResult[1].middleName,
						clientlastname: qryResult[1].lastName,
						clientcompany: qryResult[1].businessName,
						clientaddress1: qryResult[1].address1,
						clientaddress2: qryResult[1].address2,
						clientcity: qryResult[1].city,
						clientstateprov: qryResult[1].state,
						clientpostalcode: qryResult[1].postalCode,
						clientemail: qryResult[1].email,
						clientphonehome: qryResult[1].homePhone,
						clientphonecell: qryResult[1].cellPhone,
						clientphonealternate: qryResult[1].alternatePhone
					};

					var qryClientFeesData = getClientFees(clientID=qryResult[1].clientID,clientReferralID=strResult.clientReferralID);
					var qryClientFeesTotals = getFeesTotals(qryItems=qryClientFeesData);

					strResult.referral.clientfeesinfo = {
						clientfees: [],
						clientfeestotals: {}
					};
					for (var thisFee in qryClientFeesData) {
						strResult.referral.clientfeesinfo.clientfees.append({ 
							"date": dateFormat(thisFee.transactionDate,"m/d/yyyy"),
							"fee": trim(numberFormat(thisFee.referralDues,"9,999.99")),
							"paidtodate": trim(numberFormat(thisFee.paidToDate,"9,999.99")),
							"amounttobepaid": trim(numberFormat(thisFee.amtToBePaid,"9,999.99"))
						});
					}
					if (qryClientFeesTotals.recordCount eq 1) {
						strResult.referral.clientfeesinfo.clientfeestotals = {
							"fee": trim(numberFormat(qryClientFeesTotals.referralDuesTotal[1],"9,999.99")),
							"paidtodate": trim(numberFormat(qryClientFeesTotals.paidToDateTotal[1],"9,999.99")),
							"amounttobepaid": trim(numberFormat(qryClientFeesTotals.amtToBePaidTotal[1],"9,999.99"))
						};
					} else {
						strResult.referral.clientfeesinfo.clientfeestotals = {
							"fee": trim(numberFormat(0,"9,999.99")),
							"paidtodate": trim(numberFormat(0,"9,999.99")),
							"amounttobepaid": trim(numberFormat(0,"9,999.99"))
						};
					}

					if (qryResult[1].collectClientFeeFE and qryResult[1].isReferred) {
						var qryConsultationFees = getConsultationFees(clientReferralID=strResult.clientReferralID, orgID=arguments.orgID, memberid=val(qryResult[1].memberID));
						if (qryConsultationFees.recordCount){
							var qryConsultationFeesTotals = getFeesTotals(qryItems=qryConsultationFees);

							strResult.referral.consultationfeesinfo = {
								consultationfees: [],
								consultationfeestotals: {}
							};

							for (var thisFee in qryConsultationFees) {
								strResult.referral.consultationfeesinfo.consultationfees.append({ 
									"date": dateFormat(thisFee.transactionDate,"m/d/yyyy"),
									"fee": trim(numberFormat(thisFee.referralDues,"9,999.99")),
									"paidtodate": trim(numberFormat(thisFee.paidToDate,"9,999.99")),
									"amounttobepaid": trim(numberFormat(thisFee.amtToBePaid,"9,999.99"))
								});
							}

							strResult.referral.consultationfeesinfo.consultationfeestotals = {
								"fee": trim(numberFormat(qryConsultationFeesTotals.referralDuesTotal[1],"9,999.99")),
								"paidtodate": trim(numberFormat(qryConsultationFeesTotals.paidToDateTotal[1],"9,999.99")),
								"amounttobepaid": trim(numberFormat(qryConsultationFeesTotals.amtToBePaidTotal[1],"9,999.99"))
							};
						}
					}

					strResult.referral.representative = {
						repfirstname: qryResult[1].repFirstName,
						replastname: qryResult[1].repLastName,
						relationtoclient: qryResult[1].relationToClient,
						repaddress1: qryResult[1].repAddress1,
						repaddress2: qryResult[1].repAddress2,
						repcity: qryResult[1].repCity,
						repstateprov: qryResult[1].repState,
						reppostalcode: qryResult[1].repPostalCode,
						repemail: qryResult[1].repEmail,
						repphonehome: qryResult[1].repHomePhone,
						repphonecell: qryResult[1].repCellPhone,
						repphonealternate: qryResult[1].repAlternatePhone
					};

					strResult.referral.counselor = {
						counselorfirstname: qryResult[1].counselorFirstName,
						counselorlastname: qryResult[1].counselorLastName,
						counselormembernumber: qryResult[1].counselorMemberNumber
					};

					strResult.referral.counselornotes = getReferralNotes(referralID=referralID, clientReferralID=strResult.clientReferralID, noteType='C');

					strResult.referral.call = {
						calltype: qryResult[1].clientReferralType,
						callid: qryResult[1].callUID
					};

					if (qryResult[1].clientReferralType == 'Referral to Agency')
						strResult.referral.agencyname = qryResult[1].agencyName;

					if (qryResult[1].isReferred) {
						strResult.referral.lawyer = {
							lawyerfirstname: qryResult[1].lawyerFirstName,
							lawyerlastname: qryResult[1].lawyerLastName,
							lawyermembernumber: qryResult[1].lawyerMemberNumber
						};

						strResult.referral.lawyernotes = getReferralNotes(referralID=referralID, clientReferralID=strResult.clientReferralID, noteType='A');
					}

					if (val(qryResult[1].caseID)) {
						if (val(qryResult[1].allowFeeTypeMgmt))
							strResult.referral.feetype = qryResult[1].feeTypeName

						strResult.referral.case = {
							attorney: {
								attorneyfirstname: qryResult[1].lawyerFirstName,
								attorneylastname: qryResult[1].lawyerLastName,
								attorneymembernumber: qryResult[1].lawyerMemberNumber
							},
							status: qryResult[1].statusName,
							feesreportedbyclient: trim(numberFormat(val(qryResult[1].caseFees),"9,999.99")),
							casefeesinfo: {
								casefees: [],
								casefeestotals: {}
							}
						};

						var qryCaseFees = getCaseFees(referralID=referralID, orgID=arguments.orgID, caseID=val(qryResult[1].caseID));

						if (qryCaseFees.recordCount){
							var qryCaseFeesTotals = getCaseFeesTotals(qryItems=qryCaseFees);

							for (var thisFee in qryCaseFees) {
								strResult.referral.case.casefeesinfo.casefees.append({ 
									"collectedfeedate": dateFormat(thisFee.collectedFeeDate,"m/d/yyyy"),
									"collectedfee": trim(numberFormat(thisFee.collectedFee,"9,999.99")),
									"filingfee": trim(numberFormat(thisFee.filingFee,"9,999.99")),
									"referraldues": trim(numberFormat(thisFee.referralDues,"9,999.99")),
									"amounttobepaid": trim(numberFormat(thisFee.amtToBePaid,"9,999.99"))
								});
							}

							strResult.referral.case.casefeesinfo.casefeestotals = {
								"collectedfee": trim(numberFormat(qryCaseFeesTotals.collectedFeeTotal[1],"9,999.99")),
								"filingfee": trim(numberFormat(qryCaseFeesTotals.filingFeeTotal[1],"9,999.99")),
								"referraldues": trim(numberFormat(qryCaseFeesTotals.referralDuesTotal[1],"9,999.99")),
								"amounttobepaid": trim(numberFormat(qryCaseFeesTotals.amtToBePaidTotal[1],"9,999.99"))
							};
						}

						if (qryResult[1].allowFeeDiscrepancy)
							strResult.referral.case.feediscrepanciesstatuslogs = getFeeDiscrepancyStatusChangeLog(clientReferralID=strResult.clientReferralID);
					}
					else 
						strResult.referral.referralstatus = qryResult[1].statusName;

					strResult.referral.filters = getFiltersData(orgID=arguments.orgID, referralID=referralID, clientID=qryResult[1].clientID);

					if (qryResult[1].isReferred)
						strResult.referral.attorneycustomfields = renderResourceFields(siteID=arguments.siteID, csrID=qryResult[1].siteResourceID, itemID=strResult.clientReferralID, resourceType='Referrals', areaName='Attorney', itemType='AttorneyCustom');

					strResult.referral.questionanswerpath = getSearchXMLByClientID(clientID=qryResult[1].clientID);

					strResult.referral.clientcustomfields = renderResourceFields(siteID=arguments.siteID, csrID=qryResult[1].siteResourceID, itemID=strResult.clientReferralID, resourceType='ClientReferrals', areaName='ClientReferrals', itemType='ClientRefCustom');

					if (qryResult[1].isPending)
						strResult.referral['x-delete-api-uri'] = '/v1/referral/#arguments.clientReferralUID#';

					strResult.count = 1;
				}
			}
		}

		return strResult;
	}

	private array function getReferralData (required numeric orgID, required numeric siteID, required numeric referralID, required numeric clientReferralID) {
		var sqlParams = {
			orgid = { value=arguments.orgID, cfsqltype="CF_SQL_INTEGER" },
			siteid = { value=arguments.siteID, cfsqltype="CF_SQL_INTEGER" },
			referralid = { value=arguments.referralID, cfsqltype="CF_SQL_INTEGER" },
			clientreferralid = { value=arguments.clientReferralID, cfsqltype="CF_SQL_INTEGER" },
			environment = { value=environment, cfsqltype="CF_SQL_VARCHAR" }
		};

		var arrResult = queryExecute("
			SET NOCOUNT ON;
			SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

			DECLARE @siteID int = :siteid, @orgID int = :orgid, @referralID int = :referralid, @clientReferralID int = :clientreferralid,
				@environmentName VARCHAR(50) = :environment, @environmentID INT, @mainHostname varchar(80), @scheme varchar(10);
			SELECT @environmentID = environmentID FROM dbo.platform_environments WHERE environmentName = @environmentName;

			SELECT @mainHostname = sh.hostname, @scheme = CASE WHEN sh.hasssl = 1 THEN 'https' ELSE 'http' END
			FROM dbo.siteHostnames AS sh 
			INNER JOIN dbo.siteEnvironments AS se ON se.siteID = sh.siteID
				AND se.environmentID = @environmentID
				AND se.mainHostnameID = sh.hostNameID
				AND sh.siteID = @siteID;

			-- Get clients parent record
			WITH cte_client as (
				select c.clientID, c.clientParentID, c.dateCreated as calldate, cr.clientReferralID
				from dbo.ref_clients c
				inner join dbo.ref_clientReferrals cr on cr.referralID = @referralID and cr.clientID = c.clientID
					and cr.clientReferralID = @clientReferralID
				where c.referralID =@referralID
					union all
				select c.clientID, c.clientParentID, c.dateCreated as callDate, @clientReferralID as clientReferralID
				from cte_client clients
				inner join dbo.ref_clients c on c.referralID = @referralID and c.clientID = clients.clientParentID
			)
			select cr.clientID, cr.dateCreated, cr.dateLastUpdated, cr.clientReferralDate, rc.dateCaseOpened, rc.dateCaseClosed,
				c.firstName, c.middleName, c.lastName, c.businessName, c.address1, c.address2, c.city, s.name as state, 
				c.postalCode, c.email, c.homePhone, c.cellPhone, c.alternatePhone, rep.firstName as repFirstName, 
				rep.lastName as repLastName, rep.relationToClient, rep.address1 as repAddress1, rep.address2 as repAddress2, 
				rep.city as repCity, sRep.name as repState, rep.postalCode as repPostalCode, rep.email as repEmail, 
				rep.homePhone as repHomePhone, rep.cellPhone as repCellPhone, rep.alternatePhone as repAlternatePhone,
				crsrc.clientReferralSource, cr.otherSource, mCounselor2.firstname as counselorFirstName, mCounselor2.lastname as counselorLastName, 
				mCounselor2.membernumber as counselorMemberNumber, mLawyer2.firstname as lawyerFirstName, 
				mLawyer2.lastname as lawyerLastName, mLawyer2.membernumber as lawyerMemberNumber, 
				crt.clientReferralType, cr.callUID, l.languageName, cr.issueDesc, ISNULL(cr.sendSurvey,0) as sendSurvey, 
				ISNULL(cr.sendNewsBlog,0) as sendNewsBlog, crs.statusName, r.collectClientFeeFE, crs.isReferred,
				mLawyer2.memberID, rc.caseID, rc.caseFees, r.allowFeeTypeMgmt, rft.feeTypeName, a.[name] as agencyName,
				r.allowFeeDiscrepancy, ai.siteResourceID, crs.isPending, crs.isOpen, crs.isClosed, crs.isDeleted, crs.isRetainedCase
			FROM dbo.ref_clients c
			INNER JOIN dbo.ref_clientReferrals cr on cr.referralID = @referralID and cr.clientID = c.clientID
				AND cr.clientReferralID = @clientReferralID
			INNER JOIN cte_client cParent on cParent.clientReferralID = cr.clientReferralID and cParent.clientParentID is null
			INNER JOIN dbo.ref_languages as l on l.referralID = @referralID and l.languageID = cr.communicateLanguageID
			INNER JOIN dbo.ref_clientReferralStatus crs on crs.referralID = @referralID and crs.clientReferralStatusID = cr.statusID
			INNER JOIN dbo.ref_referrals as r on r.referralID = cr.referralID
			INNER JOIN dbo.cms_applicationInstances ai on ai.siteID = @siteID
					AND ai.applicationInstanceID = r.applicationInstanceID
			left outer join dbo.ref_clients rep on rep.referralID = @referralID and rep.clientID = cr.representativeID
			left outer join dbo.ref_clientReferralTypes crt on crt.referralID = @referralID and crt.clientReferralTypeID = cr.typeID
			left outer join dbo.ref_cases rc on rc.referralID = @referralID and rc.clientReferralID = cr.clientReferralID
			left outer join dbo.ref_agencies a on a.referralID = @referralID and a.agencyID = cr.agencyID
			left outer join dbo.ref_panels as p on p.referralID = @referralID and p.panelID = cr.panelID
			left outer join dbo.ams_states as s on s.stateID = c.state
			left outer join dbo.ams_states as sRep on sRep.stateID = rep.state
			left outer join dbo.ref_clientReferralSources as crsrc on crsrc.referralID = @referralID and crsrc.clientReferralSourceID = cr.sourceID
			left outer join dbo.ref_clientReferralFeeTypes as rft on rft.feeTypeID = cr.feeTypeID
			left outer join dbo.ams_members as mCounselor 
				inner join dbo.ams_members as mCounselor2 on mCounselor2.orgID = @orgID and mCounselor2.memberID = mCounselor.activeMemberID
				on mCounselor.orgID = @orgID and mCounselor.memberID = cr.enteredByMemberID
			left outer join dbo.ams_members as mLawyer
				inner join dbo.ams_members as mLawyer2 on mLawyer2.orgID = @orgID and mLawyer2.memberID = mLawyer.activeMemberID
				on mLawyer.orgID = @orgID and mLawyer.memberID = cr.memberID
			where c.referralID = @referralID;

			SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
			", 
			sqlParams, { datasource="membercentral", returntype="array" }
		);

		return arrResult;
	}

	struct function updateReferral (required numeric orgID, required numeric siteID, required string clientReferralUID, required struct referralinfo, required numeric recordedbymemberid) {
		var strResult = { referral=structNew("ordered"), clientReferralID=0, count=0, error=[], ignoredfields=[] };

		if (not len(trim(arguments.clientReferralUID)) or not isValid("GUID",arguments.clientReferralUID)) {
			return strResult;
		}

		try {
			var referralID = getReferralID(siteID=arguments.siteID);
			var incomingStatusName = '';
			strResult.clientReferralID = getClientReferralIDByUID(referralID=referralID, clientReferralUID=arguments.clientReferralUID);

			if (strResult.clientReferralID > 0) {
				var strReferral = getReferral(orgID=arguments.orgID, siteID=arguments.siteID, clientReferralUID=arguments.clientReferralUID).referral;
				var thisReferralData = getReferralData(orgID=arguments.orgID, siteID=arguments.siteID, referralID=referralID, clientReferralID=strResult.clientReferralID)[1];

				var arrMessages = [];
				var arrIgnoredFields = [];
				var arrUpdateFields = [];
				var arrValidPayloadKeys = [];
				strReferral.extData = {};

				var arrAllStates = queryExecute("
					SET NOCOUNT ON;
					SET TRANSACTION ISOLATION LEVEL SNAPSHOT;
				
					SELECT stateID, [name]
					FROM dbo.ams_states;

					SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
					", {}, { datasource="membercentral", returntype="array" }
				);

				// check for the possible fields
				var allowedFieldsList = "source,othersource,language,legalissue,agreesurvey,agreenewsletter,client,representative,call,agencyname,otheragency,feetype,case,referralstatus,filters,attorneycustomfields,clientcustomfields";
				for (var thisKey in arguments.referralinfo) {
					if (listFindNoCase(allowedFieldsList, thisKey))
						arrValidPayloadKeys.append(thisKey)
					else arrIgnoredFields.append(thisKey);
				}

				if(not arrayLen(arrValidPayloadKeys)){
					strResult.error = ["No valid keys found in the JSON object"];
					return strResult;
				}

				if(structKeyExists(arguments.referralinfo, "source")){
					if(isSimpleValue(arguments.referralinfo.source) and len(arguments.referralinfo.source)){
						var qrySource = queryExecute("
							SET NOCOUNT ON;
							SET TRANSACTION ISOLATION LEVEL SNAPSHOT;
						
							SELECT clientReferralSourceID as sourceID
							FROM dbo.ref_clientReferralSources
							WHERE referralID = :referralID
							AND clientReferralSource = :source;

							SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
							", 
							{ referralID = { value=referralID, cfsqltype="cf_sql_integer" }, source = { value=arguments.referralinfo.source, cfsqltype="cf_sql_varchar" } },
							{ datasource="membercentral" }
						);

						if(qrySource.recordCount){
							strReferral.extData.sourceID = qrySource.sourceID;
							arrUpdateFields.append("source");
						}
						else arrMessages.append("Invalid source.");
					}
					else arrMessages.append(not isSimpleValue(arguments.referralinfo.source) ? "Invalid source." : "Source cannot be empty.");

					strReferral.source = isSimpleValue(arguments.referralinfo.source) ? arguments.referralinfo.source : ""; // updating this here anyway regardless of its validity, as it will be used in subsequent checks related to othersource	
				}

				if(structKeyExists(arguments.referralinfo, "source") and strReferral.source eq "Other" and not structKeyExists(arguments.referralinfo, "othersource"))
					arrMessages.append("Other source is required.");
				elseif(strReferral.source neq "Other" and structKeyExists(arguments.referralinfo, "othersource"))
					arrIgnoredFields.append("othersource");
				elseif(strReferral.source eq "Other" and structKeyExists(arguments.referralinfo, "othersource")){
					if(isSimpleValue(arguments.referralinfo.othersource) and len(arguments.referralinfo.othersource)){
						strReferral.othersource = referralinfo.othersource;
						arrUpdateFields.append("othersource");
					}
					else arrMessages.append(not isSimpleValue(arguments.referralinfo.othersource) ? "Invalid other source." : "Other source cannot be empty.");
				}

				if (structKeyExists(arguments.referralinfo,"language")) {
					if(isSimpleValue(arguments.referralinfo.language) and len(arguments.referralinfo.language)){
						var qryLanguage = queryExecute("
							SET NOCOUNT ON;
							SET TRANSACTION ISOLATION LEVEL SNAPSHOT;
						
							SELECT languageID
							FROM dbo.ref_languages
							WHERE referralID = :referralID
							AND languageName = :language;

							SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
							", 
							{ referralID = { value=referralID, cfsqltype="cf_sql_integer" }, language = { value=arguments.referralinfo.language, cfsqltype="cf_sql_varchar" } },
							{ datasource="membercentral" }
						);

						if(qryLanguage.recordCount){
							strReferral.language = arguments.referralinfo.language;
							strReferral.extData.languageID = qryLanguage.languageID;
							arrUpdateFields.append("language");
						}
						else arrMessages.append("Invalid language.");
					}
					else arrMessages.append(not isSimpleValue(arguments.referralinfo.language) ? "Invalid language." : "Language cannot be empty.");
				}

				if (structKeyExists(arguments.referralinfo,"legalissue")) {
					if(isSimpleValue(arguments.referralinfo.legalissue) and len(arguments.referralinfo.legalissue)){
						strReferral.legalissue = arguments.referralinfo.legalissue;
						arrUpdateFields.append("legalissue");
					}
					else arrMessages.append(not isSimpleValue(arguments.referralinfo.legalissue) ? "Invalid legal issue description." : "Legal issue description cannot be empty.");
				}

				if (structKeyExists(arguments.referralinfo,"agreesurvey")) {
					if(isSimpleValue(arguments.referralinfo.agreesurvey) and listFind("0,1,true,false",arguments.referralinfo.agreesurvey)){
						strReferral.agreesurvey = arguments.referralinfo.agreesurvey;
						arrUpdateFields.append("agreesurvey");
					}
					else arrMessages.append("Invalid value for Agrees to receive surveys.");
				}

				if (structKeyExists(arguments.referralinfo,"agreenewsletter")) {
					if(isSimpleValue(arguments.referralinfo.agreenewsletter) and listFind("0,1,true,false",arguments.referralinfo.agreenewsletter)){
						strReferral.agreenewsletter = arguments.referralinfo.agreenewsletter;
						arrUpdateFields.append("agreenewsletter");
					}
					else arrMessages.append("Invalid value for Agrees to receive Newsletters/Blog e-mails.");
				}

				if(structKeyExists(arguments.referralinfo, "client")){
					if(isStruct(arguments.referralinfo.client)){
						for (var thisKey in arguments.referralinfo.client) {
							if (structKeyExists(strReferral.client, thisKey)){
								if(isSimpleValue(arguments.referralinfo.client[thisKey])){
									var thisFieldVal = arguments.referralinfo.client[thisKey];
									switch (thisKey) {
										case "clientfirstname":
											if (len(thisFieldVal) > 75)
												arrMessages.append("Client first name must be less than 75 characters.");
											elseif (thisFieldVal == "")
												arrMessages.append("Client first name cannot be empty.");
											break;
										case "clientmiddlename":
											if (len(thisFieldVal) > 25)
												arrMessages.append("Client middle name must be less than 25 characters.");
											break;
										case "clientlastname":
											if (len(thisFieldVal) > 75)
												arrMessages.append("Client last name must be less than 75 characters.");
											elseif (thisFieldVal == "")
												arrMessages.append("Client last name cannot be empty.");
											break;
										case "clientcompany":
											if (len(thisFieldVal) > 100)
												arrMessages.append("Client company must be less than 100 characters.");
											break;
										case "clientaddress1":
											if (len(thisFieldVal) > 100)
												arrMessages.append("Client address 1 must be less than 100 characters.");
											break;
										case "clientaddress2":
											if (len(thisFieldVal) > 100)
												arrMessages.append("Client address 2 must be less than 100 characters.");
											break;
										case "clientcity":
											if (len(thisFieldVal) > 100)
												arrMessages.append("Client city must be less than 100 characters.");
											break;
										case "clientstateprov":
											if (len(thisFieldVal)){
												var arrThisState = arrAllStates.filter(function(item){
													return arguments.item.name eq thisFieldVal;
												});
												if(arrayLen(arrThisState))
													strReferral.extData.clientStateID = arrThisState[1].stateID;
												else
													arrMessages.append("Invalid client state.");
											}
											break;
										case "clientpostalcode":
											if (len(thisFieldVal) > 25)
												arrMessages.append("Client zip code must be less than 25 characters.");
											break;
										case "clientemail":
											if (len(thisFieldVal) and (NOT isValid("regex",thisFieldVal,regex.email) or len(thisFieldVal) > 255))
												arrMessages.append("Invalid client email.");
											break;
										case "clientphonehome":
										case "clientphonecell":
										case "clientphonealternate":
											if (len(thisFieldVal) > 40)
												arrMessages.append("Client #replace(thisKey,"clientphone","")# phone must be less than 40 characters.");
											break;
									}

									strReferral.client[thisKey] = arguments.referralinfo.client[thisKey];
									arrUpdateFields.append("client." & thisKey);
								}
								else arrMessages.append("Invalid value for client." & thisKey);
							}
							else arrIgnoredFields.append("client." & thisKey);
						}

						if(strReferral.call["calltype"] eq "Lawyer Referral"){
							var arrContactFields = arrUpdateFields.filter(function(item){
								return listFindNoCase("client.clientemail,client.clientphonehome,client.clientphonecell,client.clientphonealternate", arguments.item);
							});

							// if tried to update any of the contact fields, make sure atleast one contact should be present
							if(arrayLen(arrContactFields) and not len(strReferral.client["clientemail"]) and not len(strReferral.client["clientphonehome"]) and not len(strReferral.client["clientphonecell"]) and not len(strReferral.client["clientphonealternate"]))
								arrMessages.append("Either one of the client telephone number or a valid client e-mail is required.");
						}
					}
					else arrMessages.append("Invalid value for client.");
				}

				if(structKeyExists(arguments.referralinfo, "representative")){
					if(isStruct(arguments.referralinfo.representative)){
						for (var thisKey in arguments.referralinfo.representative) {
							if (structKeyExists(strReferral.representative, thisKey)){
								if(isSimpleValue(arguments.referralinfo.representative[thisKey])){
									var thisFieldVal = arguments.referralinfo.representative[thisKey];
									switch (thisKey) {
										case "repfirstname":
											if (len(thisFieldVal) > 75)
												arrMessages.append("Representative first name must be less than 75 characters.");
											break;
										case "replastname":
											if (len(thisFieldVal) > 75)
												arrMessages.append("Representative last name must be less than 75 characters.");
											break;
										case "relationtoclient":
											if (len(thisFieldVal) > 100)
												arrMessages.append("Representative Relation to Client must be less than 100 characters.");
											break;
										case "repaddress1":
											if (len(thisFieldVal) > 100)
												arrMessages.append("Representative address 1 must be less than 100 characters.");
											break;
										case "repaddress2":
											if (len(thisFieldVal) > 100)
												arrMessages.append("Representative address 2 must be less than 100 characters.");
											break;
										case "repcity":
											if (len(thisFieldVal) > 100)
												arrMessages.append("Representative city must be less than 100 characters.");
											break;
										case "repstateprov":
											if (len(thisFieldVal)){
												var arrThisState = arrAllStates.filter(function(item){
													return arguments.item.name eq thisFieldVal;
												});
												if(arrayLen(arrThisState))
													strReferral.extData.repStateID = arrThisState[1].stateID;
												else
													arrMessages.append("Invalid representative state.");
											}
											break;
										case "reppostalcode":
											if (len(thisFieldVal) > 25)
												arrMessages.append("Representative zip code must be less than 25 characters.");
											break;
										case "repemail":
											if (len(thisFieldVal) and (NOT isValid("regex",thisFieldVal,regex.email) or len(thisFieldVal) > 255))
												arrMessages.append("Invalid representative email.");
											break;
										case "repphonehome":
										case "repphonecell":
										case "repphonealternate":
											if (len(thisFieldVal) > 40)
												arrMessages.append("Representative #replace(thisKey,"repphone","")# phone must be less than 40 characters.");
											break;
									}

									strReferral.representative[thisKey] = arguments.referralinfo.representative[thisKey];
									arrUpdateFields.append("representative." & thisKey);
								}
								else arrMessages.append("Invalid value for representative." & thisKey);
							}
							else arrIgnoredFields.append("representative." & thisKey);
						}
					}
					else arrMessages.append("Invalid value for representative.");
				}

				if(structKeyExists(arguments.referralinfo, "call")){
					if(not val(thisReferralData.isReferred) and not val(thisReferralData.isClosed) and not val(thisReferralData.isDeleted)){
						if(isStruct(arguments.referralinfo.call) and structKeyExists(arguments.referralinfo.call, "calltype")){
							if(isSimpleValue(arguments.referralinfo.call.calltype) and len(arguments.referralinfo.call.calltype)){
								var qryCallType = queryExecute("
									SET NOCOUNT ON;
									SET TRANSACTION ISOLATION LEVEL SNAPSHOT;
								
									SELECT clientReferralTypeID
									FROM dbo.ref_clientReferralTypes
									WHERE referralID = :referralID
									AND clientReferralType = :clientReferralType;

									SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
									", 
									{ referralID = { value=referralID, cfsqltype="cf_sql_integer" }, clientReferralType = { value=arguments.referralinfo.call.calltype, cfsqltype="cf_sql_varchar" } },
									{ datasource="membercentral" }
								);

								if(qryCallType.recordCount){
									strReferral.call.calltype = arguments.referralinfo.call.calltype;
									strReferral.extData.callTypeID = qryCallType.clientReferralTypeID;
									arrUpdateFields.append("call.calltype");
								}
								else arrMessages.append("Invalid call type.");
							}
							else arrMessages.append(not isSimpleValue(arguments.referralinfo.call.calltype) ? "Invalid call type." : "Call type cannot be empty.");
						}
						elseif(not isStruct(arguments.referralinfo.call)) arrMessages.append("Invalid value for call.");

						if(isStruct(arguments.referralinfo.call)){
							var arrInvalidKeys = listToArray(structKeyList(arguments.referralinfo.call)).filter(function(item) {
								return item != "calltype";
							});
							arrInvalidKeys = arrInvalidKeys.map(function(item) { return "call." & item; });
							arrayAppend(arrIgnoredFields, arrInvalidKeys, true);
						}
					}
					else arrIgnoredFields.append("call");
				}

				if(structKeyExists(arguments.referralinfo,"agencyname") and strReferral.call.calltype neq "Referral to Agency")
					arrIgnoredFields.append("agencyname");
				if(structKeyExists(arguments.referralinfo,"otheragency") and strReferral.call.calltype neq "Referral to Agency")
					arrIgnoredFields.append("otheragency");

				if(strReferral.call.calltype eq "Referral to Agency"){
					if (structKeyExists(arguments.referralinfo,"agencyname")) {
						if(isSimpleValue(arguments.referralinfo.agencyname) and len(arguments.referralinfo.agencyname)){
							var qryAgency = queryExecute("
								SET NOCOUNT ON;
								SET TRANSACTION ISOLATION LEVEL SNAPSHOT;
							
								SELECT agencyID
								FROM dbo.ref_agencies
								WHERE referralID = :referralID
								AND isActive = 1
								AND [name] = :agencyname;

								SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
								", 
								{ referralID = { value=referralID, cfsqltype="cf_sql_integer" }, agencyname = { value=arguments.referralinfo.agencyname, cfsqltype="cf_sql_varchar" } },
								{ datasource="membercentral" }
							);

							if(qryAgency.recordCount){
								strReferral.agencyname = arguments.referralinfo.agencyname;
								strReferral.extData.agencyID = qryAgency.agencyID;
								arrUpdateFields.append("agencyname");
							}
							else arrMessages.append("Invalid agency name.");
						}
						else arrMessages.append(not isSimpleValue(arguments.referralinfo.language) ? "Invalid Agency name." : "Agency name cannot be empty.");
					}
					if (structKeyExists(arguments.referralinfo,"otheragency")) {
						if(isSimpleValue(arguments.referralinfo.otheragency)){
							if(len(arguments.referralinfo.otheragency) > 255)
								arrMessages.append("Other agency name must be less than 255 characters.");
							elseif(len(arguments.referralinfo.otheragency)) {
								strReferral.extData.otheragency = arguments.referralinfo.otheragency;
								arrUpdateFields.append("otheragency");
							}
						}
						else arrMessages.append("Invalid value for other agency.");
					}
				}

				if (structKeyExists(arguments.referralinfo,"feetype")) {
					if(val(strReferral.caseexists) and thisReferralData.allowFeeTypeMgmt){
						if(isSimpleValue(arguments.referralinfo.feetype) and len(arguments.referralinfo.feetype)){
							var qryFeeType = queryExecute("
								SET NOCOUNT ON;
								SET TRANSACTION ISOLATION LEVEL SNAPSHOT;
							
								SELECT feeTypeID
								FROM dbo.ref_clientReferralFeeTypes
								WHERE referralID = :referralID
								AND feeTypeName = :feeTypeName;

								SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
								", 
								{ referralID = { value=referralID, cfsqltype="cf_sql_integer" }, feeTypeName = { value=arguments.referralinfo.feetype, cfsqltype="cf_sql_varchar" } },
								{ datasource="membercentral" }
							);

							if(qryFeeType.recordCount){
								strReferral.feetype = arguments.referralinfo.feetype;
								strReferral.extData.feeTypeID = qryFeeType.feeTypeID;
								arrUpdateFields.append("feetype");
							}
							else arrMessages.append("Invalid fee type.");
						}
						else arrMessages.append(not isSimpleValue(arguments.referralinfo.feetype) ? "Invalid fee type." : "Fee type cannot be empty.");
					}
					else arrIgnoredFields.append("feetype");
				}

				if (structKeyExists(referralinfo, "case")){
					if(val(strReferral.caseexists)){
						if(isStruct(referralinfo.case)){
							if(structKeyExists(referralinfo.case, "status")){
								if(isSimpleValue(referralinfo.case.status) and len(referralinfo.case.status))
									incomingStatusName = referralinfo.case.status;
								else arrMessages.append(not isSimpleValue(referralinfo.case.status) ? "Invalid value for status." : "status cannot be empty.");
							}

							if(structKeyExists(referralinfo.case, "feesreportedbyclient")){
								if(not isSimpleValue(referralinfo.case.feesreportedbyclient))
									arrMessages.append("Invalid value for Fees Reported by Client.");
								else strReferral.case.feesreportedbyclient = referralinfo.case.feesreportedbyclient;
							}

							var arrInvalidKeys = listToArray(structKeyList(arguments.referralinfo.case)).filter(function(item) {
								return not listFindNoCase("status,feesreportedbyclient",item);
							});
							arrInvalidKeys = arrInvalidKeys.map(function(item) { return "case." & item; });
							arrayAppend(arrIgnoredFields, arrInvalidKeys, true);
						}
						else arrMessages.append("Invalid value for case.");
					}
					else arrIgnoredFields.append("case");
				}

				// check if passed in status is a valid one for the current setup
				var isValidStatus = false;

				if(structKeyExists(referralinfo, "referralstatus")){
					if(NOT val(strReferral.caseexists)){
						if(val(thisReferralData.isPending) or val(thisReferralData.isClosed) or val(thisReferralData.isDeleted))
							arrMessages.append("The current configuration does not allow referralstatus updates.");
						elseif(not len(referralinfo.referralstatus))
							arrMessages.append("referralstatus cannot be empty.");
						else incomingStatusName = referralinfo.referralstatus;
					}
					else arrIgnoredFields.append("referralstatus");
				}

				if(len(incomingStatusName)){
					// get all referral status names into an array
					var arrAllReferralStatuses = queryExecute("
						SET NOCOUNT ON;
						SET TRANSACTION ISOLATION LEVEL SNAPSHOT;
					
						SELECT clientReferralStatusID as statusID, statusName, isActive, isPending, isClosed, isRetainedCase
						FROM dbo.ref_clientReferralStatus
						WHERE referralID = :referralID
						ORDER BY statusName;

						SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
						", 
						{ referralID = { value=referralID, cfsqltype="cf_sql_integer" } },
						{ datasource="membercentral", returntype="array" }
					);

					if (NOT val(strReferral.caseexists)){
						var arrValidStatus = arrAllReferralStatuses.filter(function(item){
							return arguments.item.isPending eq 0
								and (arguments.item.isActive eq 1 or arguments.item.statusName eq strReferral.referralstatus)
								and arguments.item.statusName eq referralinfo.referralstatus;
						});

						if(arrayLen(arrValidStatus)){
							isValidStatus = true;
							strReferral.referralstatus = referralinfo.referralstatus;
							strReferral.extData.statusID = arrValidStatus[1].statusID;
							arrUpdateFields.append("referralstatus");
						}
					}
					else if (val(strReferral.caseexists)) {
						var arrValidStatus = arrAllReferralStatuses.filter(function(item){
							return arguments.item.isRetainedCase eq 1
								and (val(thisReferralData.isClosed) ? arguments.item.isClosed eq 1 : true) 
								and (arguments.item.isActive eq 1 or arguments.item.statusname eq strReferral.case.status)
								and arguments.item.statusName eq referralinfo.case.status;
						});

						if(arrayLen(arrValidStatus)){
							isValidStatus = true;
							strReferral.case.status = referralinfo.case.status;
							strReferral.extData.statusID = arrValidStatus[1].statusID;
							arrUpdateFields.append("case.status");
						}
					}

					if(not isValidStatus)
						arrMessages.append("The provided status is invalid with the current configuration.");
				}

				if(structKeyExists(arguments.referralinfo, "filters")){
					if(isStruct(arguments.referralinfo.filters) and structCount(arguments.referralinfo.filters) and (val(thisReferralData.isReferred) or strReferral.call.calltype eq "Referral to Agency"))
						arrIgnoredFields.append("filters");
					elseif(isStruct(arguments.referralinfo.filters)){
						var arrAllPanels = queryExecute("
							SET NOCOUNT ON;
							SET TRANSACTION ISOLATION LEVEL SNAPSHOT;
						
							SELECT panelID, [name] AS panelName, ISNULL(panelParentID,0) AS panelParentID
							FROM dbo.ref_panels
							WHERE referralID = :referralID
							AND isActive = 1;

							SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
							",
							{ referralID = { value=referralID, cfsqltype="cf_sql_integer" } },
							{ datasource="membercentral", returntype="array" }
						);

						// assigning panel & subpanel filter values to separate variables for validating in subsequent steps
						var incomingPanelFilters = {};
						var incomingSubPanelFilters = {};
						for (var thisKey in arguments.referralinfo.filters) {
							if (structKeyExists(strReferral.filters, thisKey) and listFindNoCase("primarypanel,secondarypanel,tertiarypanel,primarysubpanel,secondarysubpanel,tertiarysubpanel", thisKey)){
								if(isSimpleValue(arguments.referralinfo.filters[thisKey])){
									if(listFindNoCase("primarypanel,secondarypanel,tertiarypanel", thisKey))
										incomingPanelFilters[thisKey] = arguments.referralinfo.filters[thisKey];
									else incomingSubPanelFilters[thisKey] = arguments.referralinfo.filters[thisKey];
								}
								else arrMessages.append("Invalid value for filters." & thisKey);
							}
							elseif(listFindNoCase("customfields,classifications", thisKey)){
								if(isArray(arguments.referralinfo.filters[thisKey])){
									if(arrayLen(arguments.referralinfo.filters[thisKey])){
										var strValidateResult;
										if(thisKey eq "customfields"){
											strValidateResult = validateFilterFSFields(orgID=arguments.orgID, referralID=referralID, arrIncomingCustomFields=arguments.referralinfo.filters[thisKey]);
											strReferral.extData.strFilterCF = strValidateResult.strFilterCF;
										}
										else {
											strValidateResult = validateFilterClassifications(orgID=arguments.orgID, referralID=referralID, arrIncomingClassifications=arguments.referralinfo.filters[thisKey]);
											strReferral.extData.strFilterClassifications = strValidateResult.strFilterClassifications;
										}
										arrayAppend(arrMessages, strValidateResult.arrMessages, true);
										arrayAppend(arrIgnoredFields, strValidateResult.arrIgnoredFields, true);
									}
								}
								else arrMessages.append("Invalid value for filters." & thisKey);
							}
							else arrIgnoredFields.append("filters." & thisKey);
						}

						var strPanelFilterMapping = { "primarypanel":"panelid1", "secondarypanel":"panelid2", "tertiarypanel":"panelid3", "primarysubpanel":"subpanelid1", "secondarysubpanel":"subpanelid2", "tertiarysubpanel":"subpanelid3" }

						// validating panels
						for (var thisKey in incomingPanelFilters) {
							var thisFieldVal = incomingPanelFilters[thisKey];

							var panelType = replace(thisKey,"panel","");
							panelType = UCase(left(panelType, 1)) & mid(panelType, 2);

							if (len(thisFieldVal) > 255)
								arrMessages.append("#panelType# panel name must be less than 255 characters.");
							elseif (not len(thisFieldVal) and thisKey eq "primarypanel")
								arrMessages.append("Primary panel name cannot be empty.");
							elseif (len(thisFieldVal)) {
								var arrValidPanel = arrAllPanels.filter(function(item){
									return arguments.item.panelParentID eq 0
										and arguments.item.panelName eq thisFieldVal;
								});

								if(arrayLen(arrValidPanel)){
									if(strReferral.filters[thisKey] neq thisFieldVal){
										// if parent panel selection changes, mark sub panel values for resetting
										strReferral.extData[strPanelFilterMapping["#lcase(panelType)#subpanel"]] = "";
									}
									strReferral.filters[thisKey] = thisFieldVal;
									strReferral.extData[strPanelFilterMapping[thisKey]] = arrValidPanel[1].panelID;
									arrUpdateFields.append("filters.#thisKey#");
								}
								else {
									arrMessages.append("Invalid value for filters.#thisKey#");
									strReferral.filters[thisKey] = ""; // sub-panels validation for this panel also needs to fail now
								}
							}
							else {
								strReferral.filters[thisKey] = ""; // sub-panels validation for this panel also needs to fail now
								strReferral.extData[strPanelFilterMapping[thisKey]] = "0"; // even for empty values, nodes to be created within xml for main panels
								strReferral.extData[strPanelFilterMapping["#lcase(panelType)#subpanel"]] = "";
							}
						}

						// validating sub-panels only after main panel validations, to ensure if they are valid children of parent
						for (var thisKey in incomingSubPanelFilters) {
							var thisFieldVal = incomingSubPanelFilters[thisKey];

							if (len(thisFieldVal)){
								var panelType = replace(thisKey,"subpanel","");

								// we already validated the parent panel from passed in filters, here we need to consider the existing values as well
								var arrValidParentPanel = arrAllPanels.filter(function(item){
									return arguments.item.panelParentID eq 0
										and arguments.item.panelName eq strReferral.filters["#lcase(panelType)#panel"];
								});

								if(arrayLen(arrValidParentPanel)){
									strReferral.filters[thisKey] = "";
									strReferral.extData[strPanelFilterMapping[thisKey]] = "";

									var arrSubPanelNames = listToArray(thisFieldVal,"|");
									for (var thisSubPanelName in arrSubPanelNames) {
										if (len(thisSubPanelName) > 255)
											arrMessages.append("Each #panelType# sub-panel name must be less than 255 characters.");
										elseif (len(thisSubPanelName)) {
											var arrValidSubPanel = arrAllPanels.filter(function(item){
												return arguments.item.panelParentID eq arrValidParentPanel[1].panelID
													and arguments.item.panelName eq thisSubPanelName;
											});

											if(arrayLen(arrValidSubPanel)){
												strReferral.filters[thisKey] = listappend(strReferral.filters[thisKey],thisSubPanelName,"|");
												strReferral.extData[strPanelFilterMapping[thisKey]] = listappend(strReferral.extData[strPanelFilterMapping[thisKey]],arrValidSubPanel[1].panelID);
											}
											else {
												arrMessages.append("Invalid value for filters.#thisKey#");
												break;
											}
										}
									}
									if(thisFieldVal eq strReferral.filters[thisKey]){
										arrUpdateFields.append("filters.#thisKey#");
									}
								}
								else arrMessages.append("No valid parent panel present for filters." & thisKey);
							}
							else strReferral.extData[strPanelFilterMapping[thisKey]] = "";
						}
					}
					else arrMessages.append("Invalid value for filters");
				}

				for (var thisKey in ["attorneycustomfields","clientcustomfields"]) {
					if(structKeyExists(arguments.referralinfo, thisKey)){
						if(isArray(arguments.referralinfo[thisKey]) and arrayLen(arguments.referralinfo[thisKey]) and (thisKey eq "attorneycustomfields" and not val(thisReferralData.isReferred)))
							arrIgnoredFields.append(thisKey);
						elseif(isArray(arguments.referralinfo[thisKey])){
							if(arrayLen(arguments.referralinfo[thisKey])){
								var strValidateResult = validateReferralCustomFields(siteID=arguments.siteID, clientReferralID=strResult.clientReferralID, strReferral=strReferral, type=thisKey, arrIncomingCustomFields=arguments.referralinfo[thisKey]);
								arrayAppend(arrIgnoredFields, strValidateResult.arrIgnoredFields, true);

								if(arrayLen(strValidateResult.arrValidIncomingFields)){
									var strUpdateQueryResult = getReferralCustomFieldsUpdateQuery(siteID=arguments.siteID, clientReferralID=strResult.clientReferralID, type=thisKey, arrIncomingCustomFields=strValidateResult.arrValidIncomingFields);
									arrayAppend(arrIgnoredFields, strUpdateQueryResult.arrIgnoredFields, true);
									arrayAppend(arrMessages, strUpdateQueryResult.arrMessages, true);
									strReferral.extData.refCustomFieldsSQL = structKeyExists(strReferral.extData, "refCustomFieldsSQL") ? strReferral.extData.refCustomFieldsSQL & strUpdateQueryResult.refCustomFieldsSQL : strUpdateQueryResult.refCustomFieldsSQL;
									if (structKeyExists(strReferral.extData, "arrRefFieldChanges"))
										arrayAppend(strReferral.extData.arrRefFieldChanges, strUpdateQueryResult.arrRefFieldChanges, true);
									else strReferral.extData.arrRefFieldChanges = strUpdateQueryResult.arrRefFieldChanges;
								}
							}
						}
						else arrMessages.append("Invalid value for " & thisKey);
					}
				}
				
				if (arrayLen(arrMessages))
					strResult.error = arrMessages;
				else {
					var strUpdateResult = updateClientReferralData(orgID=arguments.orgID, referralID=referralID, clientReferralID=strResult.clientReferralID, strReferral=strReferral, recordedbymemberid=recordedbymemberid);
					if(strUpdateResult.success){
						strResult = getReferral(orgID=arguments.orgID, siteID=arguments.siteID, clientReferralUID=arguments.clientReferralUID);
						strResult.ignoredfields = arrIgnoredFields;
					}
				}
			}
		} catch (any e) {
			strResult.count = -1;
		}

		return strResult;
	}

	string function deleteReferral (required numeric orgid, required numeric siteID, required string clientReferralUID, required numeric recordedbymemberid) {
		// invalid uid
		if (not len(trim(arguments.clientReferralUID)) or not isValid("GUID",arguments.clientReferralUID)) return 'referralNotFound';

		var referralID = getReferralID(siteID=arguments.siteID);
		if (referralID EQ 0) return 'referralNotFound';

		var clientReferralID = getClientReferralIDByUID(referralID=referralID, clientReferralUID=arguments.clientReferralUID);
		if (clientReferralID EQ 0) return 'referralNotFound';

		var strClientReferral = getReferralData(orgID=arguments.orgID, siteID=arguments.siteID, referralID=referralID, clientReferralID=clientReferralID)[1];
		if (NOT strClientReferral.isPending OR strClientReferral.isDeleted) return 'invalidReferral';

		// delete client referral
		var qryDeletedStatus = getClientReferralStatus(referralID=referralID, isDeleted=1);
		updateClientReferralStatus(referralID=referralID, clientReferralID=clientReferralID, statusID=qryDeletedStatus.clientReferralStatusID);

		// log
		addReferralUpdateHistory(orgID=arguments.orgID, actorMemberID=arguments.recordedbymemberid, clientReferralID=clientReferralID, mainMessage='Referral Status Changed (API)',
			changes=[{ITEM="Status", OLDVALUE=strClientReferral.statusName, NEWVALUE=qryDeletedStatus.statusName}]);

		var qryClientPaymentData = getClientPaymentData(orgID=arguments.orgID, referralID=referralID, clientID=strClientReferral.clientID);
		if (qryClientPaymentData.recordCount) {
			removeClientPaymentInfo(orgID=arguments.orgID, referralID=referralID, clientID=strClientReferral.clientID, recordedbymemberid=arguments.recordedbymemberid);
			adjustClientReferralFeeToZero(orgID=arguments.orgID, siteID=arguments.siteID, qryClientPaymentData=qryClientPaymentData, recordedbymemberid=arguments.recordedbymemberid);
		}

		// reprocess conditions
		reprocessConditions(referralID=referralID, clientReferralID=clientReferralID);

		return "ok";
	}

	private void function updateClientReferralStatus (required numeric referralID, required numeric clientReferralID, required numeric statusID) {
		var qryUpdateStatus = queryExecute("
				UPDATE dbo.ref_clientReferrals
				SET statusID = :statusID
				WHERE clientReferralID = :clientReferralID
				AND referralID = :referralID
			", 
			{
				referralID = {value=arguments.referralID, cfsqltype="cf_sql_integer"},
				clientReferralID = {value=arguments.clientReferralID, cfsqltype="cf_sql_integer"},
				statusID = { value=arguments.statusID, cfsqltype="cf_sql_integer" }
			},
			{ datasource="membercentral" }
		);
	}

	private struct function validateFilterFSFields (required numeric orgID, required numeric referralID, required array arrIncomingCustomFields) {
		var strResult = { strFilterCF:{}, arrIgnoredFields:[], arrMessages:[] };

		var xmlFields = getMemberFieldsXMLForReferralSearch(referralID=arguments.referralID, orgID=arguments.orgID);
		if (arrayLen(xmlFields.xmlRoot.xmlChildren)) {
			var arrFSFieldLabels = [];
			var arrIncomingCFLabels = [];
			var arrXMLFieldsForValidation = [];
			for (var thisField in arguments.arrIncomingCustomFields) {
				arrayAppend(arrIncomingCFLabels, thisField.label);
			}
			for (var currentfield in xmlFields.xmlRoot.xmlChildren) {
				arrayAppend(arrFSFieldLabels, currentfield.xmlattributes.fieldLabel);
				if(arrayFind(arrIncomingCFLabels, currentfield.xmlattributes.fieldLabel)){
					arrayAppend(arrXMLFieldsForValidation, currentfield)
				}
			}
			// check for invalid field labels
			for (var i = 1; i <= arrayLen(arrIncomingCFLabels); i++) {
				if (!arrayFind(arrFSFieldLabels, arrIncomingCFLabels[i])) {
					strResult.arrIgnoredFields.append("filters.customfields: " & arrIncomingCFLabels[i]);
				}
			}

			var arrOptionValues = [];
			var isInvalidValue = false;
			var hasMatch = false;
			var incomingValue = "";
			// iterating over each FS field and validate the incoming value
			for (var currentfield in arrXMLFieldsForValidation) {
				isInvalidValue = false;
				thisLabel = currentfield.xmlattributes.fieldLabel;
				var arrThisEntry = arrayFilter(arguments.arrIncomingCustomFields, function(item) {
					return item.label eq thisLabel;
				});
				incomingValue = arrThisEntry[1]["value"];

				if(not len(incomingValue)){
					strResult.strFilterCF[currentfield.xmlattributes.fieldCode] = "";
				}
				else {
					switch (currentfield.xmlAttributes.displayTypeCode) {
						case "TEXTBOX":
							if (reFindNoCase('mat?_[0-9]+_postalcode', currentfield.xmlattributes.fieldCode) || findNoCase('_proximity', currentfield.xmlattributes.dbField)) {
								// splitting the incoming value to fetch radius and zipcode
								var arrParts = listToArray(incomingValue," ");
								if(arrayLen(arrParts) eq 5 and lcase(arrParts[1]) eq "within" and lcase(arrParts[3]) eq "miles"){
									strResult.strFilterCF[currentfield.xmlattributes.fieldCode & "_radius"] = arrParts[2];
									strResult.strFilterCF[currentfield.xmlattributes.fieldCode] = arrParts[5] neq "--" ? arrParts[5] : "";
								}
								elseif(arrayLen(arrParts) eq 1 and arrParts[1] neq "--")
									strResult.strFilterCF[currentfield.xmlattributes.fieldCode] = arrParts[1];
								else isInvalidValue = true;
							} else {
								strResult.strFilterCF[currentfield.xmlattributes.fieldCode] = incomingValue;
							}
							break;

						case "RADIO":
							arrOptionValues = [];
							for (var thisOpt in currentfield.xmlChildren) {
								switch (currentfield.xmlAttributes.dataTypeCode) {
									case "STRING":
										thisOptColValue = thisOpt.xmlAttributes.columnValueString;
										break;
									case "DECIMAL2":
										thisOptColValue = thisOpt.xmlAttributes.columnValueDecimal2;
										break;
									case "INTEGER":
										thisOptColValue = thisOpt.xmlAttributes.columnValueInteger;
										break;
									case "DATE":
										thisOptColValue = dateFormat(replaceNoCase(thisOpt.xmlAttributes.columnValueDate, 'T', ' '), "mm/dd/yyyy");
										break;
									case "BIT":
										thisOptColValue = thisOpt.xmlAttributes.columnValueBit;
										break;
									default:
										thisOptColValue = "";
								}
								arrayAppend(arrOptionValues, thisOptColValue);
							}

							if(currentfield.xmlAttributes.dataTypeCode == "BIT"){
								if(incomingValue eq "Yes") incomingValue = "1";
								elseif(incomingValue eq "No") incomingValue = "0";
							}

							if (arrayFind(arrOptionValues, incomingValue)) {
								strResult.strFilterCF[currentfield.xmlattributes.fieldCode] = incomingValue;
							}
							else isInvalidValue = true;

							break;

						case "SELECT":
						case "CHECKBOX":
							if (reFindNoCase('ma_[0-9]+_stateprov', currentfield.xmlattributes.fieldCode)) {
								var qryState = queryExecute("
									select s.code as stateCode
									from (
										select distinct ma.stateid
										from dbo.ams_memberAddressTypes mat
										inner join dbo.ams_memberAddresses as ma on mat.addressTypeID = ma.addressTypeID
											and mat.addressTypeOrder = 1
											and mat.orgID = :orgID
											and ma.stateID is not null
									) as tmp
									inner join dbo.ams_states as s on tmp.stateID = s.stateID
									where s.[name] = :stateName;
									", 
									{ stateName = { value=incomingValue, cfsqltype="cf_sql_varchar" }, orgID = { value=arguments.orgID, cfsqltype="cf_sql_integer" } },
									{ datasource="membercentral" }
								);

								if(qryState.recordCount){
									strResult.strFilterCF[currentfield.xmlattributes.fieldCode] = qryState.stateCode;
								}
								else isInvalidValue = true;
							} else if (listFindNoCase("m_recordtypeid,m_membertypeid,m_status", currentfield.xmlattributes.fieldCode)) {
								hasMatch = false;
								for (var thisOpt in currentfield.xmlChildren) {
									if(thisOpt.xmlAttributes.columnValueString eq incomingValue){
										strResult.strFilterCF[currentfield.xmlattributes.fieldCode] = thisOpt.xmlAttributes.valueID;
										hasMatch = true;
										break;
									}
								}
								if(!hasMatch) isInvalidValue = true;
							} else {
								var arrFieldValues = [];
								var thisOptColValue = "";
								var thisOptColDisplay = "";

								arrOptionValues = [];
								for (var thisOpt in currentfield.xmlChildren) {
									switch (currentfield.xmlAttributes.dataTypeCode) {
										case "STRING":
											thisOptColValue = thisOpt.xmlAttributes.columnValueString;
											break;
										case "DECIMAL2":
											thisOptColValue = thisOpt.xmlAttributes.columnValueDecimal2;
											break;
										case "INTEGER":
											thisOptColValue = thisOpt.xmlAttributes.columnValueInteger;
											break;
										case "DATE":
											thisOptColValue = dateFormat(replaceNoCase(thisOpt.xmlAttributes.columnValueDate, 'T', ' '), "mm/dd/yyyy");
											break;
										case "BIT":
											thisOptColValue = thisOpt.xmlAttributes.columnValueBit;
											break;
										default:
											thisOptColValue = "";
									}

									arrayAppend(arrOptionValues, thisOptColValue);
								}

								if(currentfield.xmlAttributes.dataTypeCode == "BIT"){
									if(incomingValue eq "Yes") incomingValue = 1;
									elseif(incomingValue eq "No") incomingValue = 0;
								}

								var arrIncomingValues = currentfield.xmlAttributes.allowMultiple ? listToArray(incomingValue,"|") : [incomingValue];
								var valueToSave = "";
								for (var thisListValue in arrIncomingValues) {
									if(arrayFind(arrOptionValues, thisListValue)){
										valueToSave = listAppend(valueToSave, thisListValue);
									}
									else {
										isInvalidValue = true;
										break;
									}
								}

								if(not isInvalidValue)
									strResult.strFilterCF[currentfield.xmlattributes.fieldCode] = valueToSave;
							}
							break;

						case "DATE":
							if(isDate(incomingValue))
								strResult.strFilterCF[currentfield.xmlattributes.fieldCode] = incomingValue;
							else isInvalidValue = true;
							break;
					}

					if(isInvalidValue)
						strResult.arrMessages.append("Invalid value for filters.customfields entry: " & thisLabel);
				}
			}
		}
		else strResult.arrIgnoredFields.append("filters.customfields");

		return strResult;
	}

	private struct function validateFilterClassifications (required numeric orgID, required numeric referralID, required array arrIncomingClassifications) {
		var strResult = { strFilterClassifications:{}, arrIgnoredFields:[], arrMessages:[] };

		var qryClassifications = getClassifications(referralID=arguments.referralID);

		if(qryClassifications.recordCount){
			var arrIncomingClassificationLabels = [];
			var arrValidClassificationLabels = [];
			var strClassificationsForValidation = {};
			for (var thisField in arguments.arrIncomingClassifications) {
				arrayAppend(arrIncomingClassificationLabels, thisField.label);
			}
			for (var row in qryClassifications) {
				thisLabel = len(trim(qryClassifications.name)) ? qryClassifications.name : qryClassifications.groupSetName;
				arrayAppend(arrValidClassificationLabels, thisLabel);
				if(arrayFind(arrIncomingClassificationLabels, thisLabel)){
					strClassificationsForValidation[qryClassifications.groupSetID] = {
						"key": "mg_gid_" & qryClassifications.groupSetID,
						"label": thisLabel
					}
				}
			}
			// check for invalid field labels
			for (var i = 1; i <= arrayLen(arrIncomingClassificationLabels); i++) {
				if (!arrayFind(arrValidClassificationLabels, arrIncomingClassificationLabels[i])) {
					strResult.arrIgnoredFields.append("filters.classifications: " & arrIncomingClassificationLabels[i]);
				}
			}

			if(structCount(strClassificationsForValidation)){
				var qryAllGroupSetGroups = queryExecute("
					select mgsg.groupSetID, mgsg.groupSetGroupID, isnull(mgsg.labelOverride,g.groupName) as groupLabel
					from dbo.ams_memberGroupSetGroups as mgsg
					inner join dbo.ams_groups g on g.groupID = mgsg.groupID
						and mgsg.groupSetID in (:groupSetIDList);", 
					{ groupSetIDList = { value="0#StructKeyList(strClassificationsForValidation)#", cfsqltype="CF_SQL_INTEGER", list="true" } },
					{ datasource="membercentral" }
				);

				var isInvalidValue;
				for (var thisGroupSetID in strClassificationsForValidation) {
					var thisClassificationLabel = strClassificationsForValidation[thisGroupSetID]["label"];
					var thisClassificationKey = strClassificationsForValidation[thisGroupSetID]["key"];

					var arrThisIncomingEntry = arrayFilter(arguments.arrIncomingClassifications, function(item) {
						return item.label eq thisClassificationLabel;
					});
					var arrIncomingValueList = listToArray(arrThisIncomingEntry[1]["value"],"|");

					var valueToSave = "";
					isInvalidValue = false;
					for (var thisGroupName in arrIncomingValueList) {
						var qryThisGroupSetGroups = queryExecute("
							select groupSetGroupID
							from qryAllGroupSetGroups
							where groupSetID = :groupSetID
							and groupLabel = :groupName;",
							{ groupSetID = { value=thisGroupSetID, cfsqltype="cf_sql_integer" }, groupName = { value=thisGroupName, cfsqltype="cf_sql_varchar" } },
							{ dbtype="query" }
						);

						if(qryThisGroupSetGroups.recordCount){
							valueToSave = listAppend(valueToSave, qryThisGroupSetGroups.groupSetGroupID);
						}
						else {
							isInvalidValue = true;
							break;
						}
					}

					if(not isInvalidValue)
						strResult.strFilterClassifications[thisClassificationKey] = valueToSave;
					else strResult.arrMessages.append("Invalid value for filters.classifications entry: " & thisClassificationLabel);
				}
			}
		}
		else strResult.arrIgnoredFields.append("filters.classifications");

		return strResult;
	}

	private struct function validateReferralCustomFields (required numeric siteID, required numeric clientReferralID, required struct strReferral, required string type, required array arrIncomingCustomFields) {
		var strResult = { arrIgnoredFields:[], arrValidIncomingFields:[] };
		var arrAllFields = [];

		if(structKeyExists(arguments.strReferral, type)){
			// loop through each fieldgrouping and add all fields into arrAllFields
			for (thisGroup in arguments.strReferral[type].fieldgroupings) {
				arrayAppend(arrAllFields, thisGroup.fields, true);
			}

			for (var currentField in arrIncomingCustomFields) {
				var thisfieldID = currentField["field_api_id"];
				var fieldMatchIndex = arrayFind(arrAllFields, function(strField) { return strField["field_api_id"] eq thisfieldID });
				if(fieldMatchIndex gt 0){
					strResult.arrValidIncomingFields.append(currentField);
				}
				else {
					strResult.arrIgnoredFields.append(type & ": " & thisfieldID);
				}
			}
		}
		else strResult.arrIgnoredFields.append(type);

		return strResult;
	}

	private struct function getReferralCustomFieldsUpdateQuery (required numeric siteID, required numeric clientReferralID, required string type, required array arrIncomingCustomFields) {
		var strResult = { arrIgnoredFields:[], arrMessages:[], refCustomFieldsSQL:"", arrRefFieldChanges:[] };

		var resourceType = ""; var areaName = ""; var itemType = ""; var prependUsageName = "";
		if (arguments.type eq "attorneycustomfields"){
			resourceType = "Referrals";
			areaName = "Attorney";
			itemType = "AttorneyCustom";
			prependUsageName = "Attorney Field";
		}
		else {
			resourceType = "ClientReferrals";
			areaName = "ClientReferrals";
			itemType = "ClientRefCustom";
			prependUsageName = "Client Field";
		}

		var qryReferralAdminSRID = queryExecute(
			"SELECT dbo.fn_getSiteResourceIDForResourceType('Referrals',:siteID) as siteResourceID;",
			{ siteID = { value=arguments.siteID, cfsqltype="CF_SQL_INTEGER" } },
			{ datasource="membercentral", cachedWithin: "request" }
		);
		
		var referralsCustomFieldsXML = getFieldsXML(siteID=arguments.siteID, resourceType=resourceType, areaName=areaName, csrid=qryReferralAdminSRID.siteResourceID);
		var customFieldArr = xmlParse(referralsCustomFieldsXML.returnXML).xmlRoot.xmlChildren;

		/* put custom fields and field types into array */
		var arrRefCustomFields = [];
		if (arrayLen(customFieldArr) AND arrayLen(arguments.arrIncomingCustomFields)){
			for (var thisIncomingField in arguments.arrIncomingCustomFields) {
				var fieldMatchIndex = arrayFind(customFieldArr, function(field) { return field.xmlattributes.uid eq thisIncomingField["field_api_id"] });
				if (fieldMatchIndex gt 0){
					var tmpAtt = customFieldArr[fieldMatchIndex].xmlattributes;

					var valueToSave = "";
					var arrInvalidOptions = [];
					var currentFieldValue = thisIncomingField["value"];
					if(len(currentFieldValue)){
						if (listFindNoCase("SELECT,RADIO,CHECKBOX",tmpAtt.displayTypeCode)){
							var qryFieldOptions = getFieldOptions(fieldID=tmpAtt.fieldID);
							for (var thisValue in listToArray(currentFieldValue,"|")) {
								var qryField = queryExecute("
									select valueID
									from qryFieldOptions
									where fieldValue = :fieldValue;",
									{ fieldValue = { value=thisValue, cfsqltype="cf_sql_varchar" } },
									{ dbtype="query" }
								);
								if(qryField.recordCount){
									valueToSave = listAppend(valueToSave, qryField.valueID);
								}
								else {
									arrayAppend(arrInvalidOptions, thisValue);
								}
							}
						}
						else {
							if ((tmpAtt.dataTypeCode eq "DATE" and not isDate(currentFieldValue)) OR
								(tmpAtt.dataTypeCode eq "INTEGER" and (not IsNumeric(currentFieldValue) or Find(".", currentFieldValue) GT 0)) OR
								(tmpAtt.dataTypeCode eq "DECIMAL2" and not IsNumeric(currentFieldValue))){
								arrayAppend(arrInvalidOptions, currentFieldValue);
							}
							else {
								valueToSave = currentFieldValue;
							}
						}
					}

					if(arrayLen(arrInvalidOptions)){
						strResult.arrMessages.append("Invalid " & (arrayLen(arrInvalidOptions) gt 1 ? "values" : "value") & " for #arguments.type# entry: " & tmpAtt.uid);
					}
					elseif(listFindNoCase("DOCUMENTOBJ,LABEL,FEATUREDIMG", tmpAtt.dataTypeCode)) {
						strResult.arrIgnoredFields.append(type & ": " & thisIncomingField["field_api_id"]);
					}
					else {
						var tmpStr = {
							fieldID=tmpAtt.fieldID,
							displayTypeCode=tmpAtt.displayTypeCode,
							dataTypeCode=tmpAtt.dataTypeCode,
							fieldText=tmpAtt.fieldText,
							value=valueToSave
						};
						arrayAppend(arrRefCustomFields,tmpStr);
					}
				}
			}
		}

		if (not arrayLen(strResult.arrMessages) and arrayLen(arrRefCustomFields)){
			var strResult.arrRefFieldChanges.addAll(getFieldChanges(arrFieldData=arrRefCustomFields, itemID=arguments.clientReferralID, itemType=itemType, prependUsageName=prependUsageName));

			savecontent variable="strResult.refCustomFieldsSQL" {
				for (var currentField in arrRefCustomFields) {
					if (listFindNoCase("SELECT,RADIO,CHECKBOX",currentField.displayTypeCode)){
						tempSQL = editRef_cf_option(itemType=itemType, itemID=arguments.clientReferralID, fieldID=currentField.fieldID, valueIDList=currentField.value, itemIDSQLVar='@clientReferralID');
					}
					else {
						tempSQL = editRef_cf_nonOption(itemType=itemType, itemID=arguments.clientReferralID, fieldID=currentField.fieldID, customText=currentField.value, itemIDSQLVar='@clientReferralID');
					}
					writeOutput(tempSQL);
				}
			};
		}

		return strResult;
	}

	public string function editRef_cf_option(required string itemType, required numeric itemID, required numeric fieldID, required string valueIDList, required string itemIDSQLVar){
		/* existing options */
		var qryExistingOptions = queryExecute("
				select dataID, valueID
				from dbo.cf_fieldData 
				where fieldID = :fieldID
				and itemID = :itemID
				and itemType = :itemType
			",
			{
				fieldID = { value=arguments.fieldID, cfsqltype="CF_SQL_INTEGER" },
				itemID = { value=arguments.itemID, cfsqltype="CF_SQL_INTEGER" },
				itemType = { value=arguments.itemType, cfsqltype="CF_SQL_VARCHAR" }
			},
			{ datasource="membercentral" }
		);

		var qryOptionsToRemove = queryExecute("
			select dataID, valueID
			from qryExistingOptions
			where valueID NOT IN (:valueIDList);",
			{ valueIDList = { value="0#arguments.valueIDList#", cfsqltype="CF_SQL_INTEGER", list="true" } },
			{ dbtype="query" }
		);
		
		/* get any options we need to add */
		var optionsToAdd = "";
		if (listLen(arguments.valueIDList)){
			var qryOptionsToAdd = queryExecute("
					select valueID
					from dbo.cf_fieldValues
					where fieldID = :fieldID
					and valueID IN (:valueIDList)
					and valueID NOT IN (:existingValueIDList);
				",
				{
					fieldID = { value=arguments.fieldID, cfsqltype="CF_SQL_INTEGER" },
					valueIDList = { value="0#arguments.valueIDList#", cfsqltype="CF_SQL_INTEGER", list="true" },
					existingValueIDList = { value="0#valueList(qryExistingOptions.valueID)#", cfsqltype="CF_SQL_INTEGER", list="true" }
				},
				{ datasource="membercentral" }
			);

			optionsToAdd = valueList(qryOptionsToAdd.valueID);
		}

		savecontent variable="editClientCustomFieldSQL" {
			for (var i = 1; i <= qryOptionsToRemove.recordCount; i++) {
				writeOutput('set @dataID = #val(qryOptionsToRemove.dataID[i])#;');
				writeOutput('DELETE FROM dbo.cf_fieldData WHERE dataID = @dataID;');
			}

			if (len(optionsToAdd)){
				var tempSQL = addRef_cf_option(itemType=arguments.itemType, fieldID=arguments.fieldID, valueIDList=local.optionsToAdd, itemIDSQLVar=arguments.itemIDSQLVar);
				writeOutput('#tempSQL#');
			}
		};

		return editClientCustomFieldSQL;
	}

	public string function editRef_cf_nonOption(required string itemType, required numeric itemID, required numeric fieldID, required string customText, required string itemIDSQLVar){
		var qryGetDataID = queryExecute("
				select dataID 
				from dbo.cf_fieldData
				where fieldID = :fieldID
				and itemID = :itemID
				and itemType = :itemType
			",
			{
				fieldID = { value=arguments.fieldID, cfsqltype="CF_SQL_INTEGER" },
				itemID = { value=arguments.itemID, cfsqltype="CF_SQL_INTEGER" },
				itemType = { value=arguments.itemType, cfsqltype="CF_SQL_VARCHAR" }
			},
			{ datasource="membercentral" }
		);

		if (NOT local.qryGetDataID.recordcount)
			editClientCustomFieldSQL = addRef_cf_nonOption(itemType=arguments.itemType, fieldID=arguments.fieldID, customText=arguments.customText, itemIDSQLVar=arguments.itemIDSQLVar);
		else {
			savecontent variable="editClientCustomFieldSQL" {
				writeOutput("set @dataID = #local.qryGetDataID.dataID#;");

				if (len(arguments.customText)){
					writeOutput("
						set @detail = '#replace(arguments.customText,"'","''","ALL")#';
						set @fieldID = #arguments.fieldID#;
						set @valueID = null;
						EXEC dbo.cf_createFieldValue @fieldID=@fieldID, @fieldValue=@detail, @amount=0, @inventory=null, @enteredByMemberID=NULL, @skipAuditLog=1, @valueID=@valueID OUTPUT;
						UPDATE dbo.cf_fieldData SET valueID = @valueID WHERE dataID = @dataID;
					");
				}
				else {
					writeOutput("DELETE FROM dbo.cf_fieldData WHERE dataID = @dataID;");
				}
			};
		}

		return editClientCustomFieldSQL;
	}

	public string function addRef_cf_option(required string itemType, required numeric fieldID, required string valueIDList, required string itemIDSQLVar){
		savecontent variable="addClientCustomFieldSQL" {
			for (var thisValueID in listToArray(arguments.valueIDList)) {
				if(thisValueID gt 0){
					writeOutput("
						set @fieldID = #arguments.fieldID#;
						set @valueID = #val(thisValueID)#;
						set @dataID = null;
						EXEC dbo.cf_setFieldData @fieldID=@fieldID, @itemID=#arguments.itemIDSQLVar#, @itemType='#arguments.itemType#', @valueID=@valueID, @fieldValue=NULL, @dataID=@dataID OUTPUT;
					");
				}
			}
		};

		return addClientCustomFieldSQL;
	}

	public string function addRef_cf_nonOption(required string itemType, required numeric fieldID, required string customText, required string itemIDSQLVar){
		var addClientCustomFieldSQL = '';
		if(len(arguments.customText)){
			savecontent variable="addClientCustomFieldSQL" {
				writeOutput("
					set @fieldID = #arguments.fieldID#;
					set @detail = '#replace(arguments.customText,"'","''","ALL")#';
					set @dataID = null;
					EXEC dbo.cf_setFieldData @fieldID=@fieldID, @itemID=#arguments.itemIDSQLVar#, @itemType='#arguments.itemType#', @valueID=null, @fieldValue=@detail, @dataID=@dataID OUTPUT;
				");
			};
		}

		return addClientCustomFieldSQL;
	}

	public array function getFieldChanges(required array arrFieldData, required numeric itemID, required string itemType, string prependUsageName = ""){
		var arrFieldChanges = [];
		var qryFieldResponses = getResponses(itemType=arguments.itemType, itemID=arguments.itemID);

		for (var thisCF in arguments.arrFieldData) {
			var qryThisFieldResponse = queryExecute("
				SELECT valueID, customValue
				FROM qryFieldResponses
				WHERE fieldID = :fieldID;",
				{ fieldID = { value=val(thisCF.fieldID), cfsqltype="cf_sql_integer" } },
				{ dbtype="query" }
			);
			
			var thisFieldValueIDList = "";
			var thisFieldValue = "";
			if (qryThisFieldResponse.recordCount){
				thisFieldValueIDList = valueList(qryThisFieldResponse.valueID);
				thisFieldValue = valueList(qryThisFieldResponse.customValue);
			}

			var thisItem = len(arguments.prependUsageName) ? "[#arguments.prependUsageName#] #thisCF.fieldText#" : thisCF.fieldText;

			/* formatting for comparison */
			if (thisCF.displayTypeCode eq "TEXTBOX" and thisCF.dataTypeCode eq "DECIMAL2" and len(thisCF.value)){
				thisCF.value = NumberFormat(thisCF.value,"0.00");
			}
			elseif (thisCF.displayTypeCode eq "DATE" and thisCF.dataTypeCode eq "DATE" and len(thisCF.value)){
				thisCF.value = dateFormat(thisCF.value, 'mm/dd/yyyy');
			}
			
			if (listFindNoCase("SELECT,RADIO,CHECKBOX",thisCF.displayTypeCode)){
				if (listSort(thisFieldValueIDList,'Numeric') NEQ listSort(thisCF.value,'Numeric')){
					if (listLen(thisCF.value)){
						qryNewFieldValues = getFieldOptions(fieldID=thisCF.fieldID, restrictToValueIDList=thisCF.value);
						newFieldValues = qryNewFieldValues.recordCount GT 0 ? valueList(qryNewFieldValues.fieldValue) : "";
					} else {
						newFieldValues = "";
					}
					var thisChange = { ITEM=thisItem, OLDVALUE=thisFieldValue, NEWVALUE=newFieldValues };
					arrayAppend(arrFieldChanges,thisChange);
				}
			}
			elseif (Compare(thisFieldValue,thisCF.value)){
				var thisChange = { ITEM=thisItem, OLDVALUE=thisFieldValue, NEWVALUE=thisCF.value };
				arrayAppend(arrFieldChanges,thisChange);
			}
		}

		return arrFieldChanges;
	}

	private struct function updateClientReferralData (required numeric orgID, required numeric referralID, required numeric clientReferralID, required struct strReferral, required numeric recordedbymemberid) {
		var strResult = { "success":true, errmsg:"" }

		try {
			var qryCurrentStatus = queryExecute(
				"SELECT statusID FROM dbo.ref_clientReferrals WHERE clientReferralID = :clientReferralID;", 
				{ clientReferralID = { value=arguments.clientReferralID, cfsqltype="cf_sql_integer" } },
				{ datasource="membercentral" }
			);
			var prevStatusID = val(qryCurrentStatus.statusID);

			var qryUpdate = queryExecute("
				SET XACT_ABORT, NOCOUNT ON;
				BEGIN TRY
					DECLARE @orgID int, @siteID int, @referralID int = :referralID, @clientReferralID int = :clientReferralID,
						@firstName varchar(75) = :firstName, @middleName varchar(25) = NULLIF(:middleName, ''), @lastName varchar(75) = :lastName,
						@businessName varchar(100) = NULLIF(:businessName, ''), @address1 varchar(100) = NULLIF(:address1, ''), @address2 varchar(100) = NULLIF(:address2, ''),
						@city varchar(100) = NULLIF(:city, ''), @postalCode varchar(25) = NULLIF(:postalCode, ''), @email varchar(255) = NULLIF(:email, ''),
						@homePhone varchar(40) = NULLIF(:homePhone, ''), @cellPhone varchar(40) = NULLIF(:cellPhone, ''), @alternatePhone varchar(40) = NULLIF(:alternatePhone, ''),
						@homePhoneE164 varchar(40), @cellPhoneE164 varchar(40), @alternatePhoneE164 varchar(40),
						@repFirstName varchar(75) = :repFirstName, @repLastName varchar(75) = :repLastName,
						@repAddress1 varchar(100) = NULLIF(:repAddress1, ''), @repAddress2 varchar(100) = NULLIF(:repAddress2, ''),
						@repCity varchar(100) = NULLIF(:repCity, ''), @repPostalCode varchar(25) = NULLIF(:repPostalCode, ''), @repEmail varchar(255) = NULLIF(:repEmail, ''),
						@repHomePhone varchar(40) = NULLIF(:repHomePhone, ''), @repCellPhone varchar(40) = NULLIF(:repCellPhone, ''), @repAlternatePhone varchar(40) = NULLIF(:repAlternatePhone, ''),
						@repHomePhoneE164 varchar(40), @repCellPhoneE164 varchar(40), @repAlternatePhoneE164 varchar(40),
						@relationToClient varchar(100) = NULLIF(:relationToClient, ''),
						@otherSource varchar(100) = NULLIF(:otherSource, ''), @issueDesc varchar(max) = :issueDesc,
						@sendSurvey bit = :sendSurvey, @sendNewsBlog bit = :sendNewsBlog,
						@recordedByMemberID int = NULLIF(:recordedByMemberID, 0), @clientParentID int, @repParentID int,
						@counselorMemberID int, @caseID int, @currentCaseFees decimal(18,2), @caseFees decimal(18,2) = :caseFees,
						@nowDate datetime = getdate(), @message varchar(max);
					
					DECLARE @clientID int, @repID int, @clientStateID int = NULLIF(:clientStateID,0), @currentClientStateID int,
						@repStateID int = NULLIF(:repStateID,0), @currentRepStateID int, @languageID int = NULLIF(:languageID,0),
						@currentLanguageID int, @statusID int = NULLIF(:statusID, 0), @currentStatusID int,
						@sourceID int = NULLIF(:sourceID,0), @currentSourceID int, @callTypeID int = NULLIF(:callTypeID, 0),
						@currentCallTypeID int, @feeTypeID int = NULLIF(:feeTypeID, 0), @currentFeeTypeID int,
						@agencyID int = NULLIF(:agencyID, 0), @currentAgencyID int, @otherAgency varchar(255) = :otherAgency,
						@panelID int = NULLIF(:panelID, 0), @currentPanelID int;
					
					SELECT @clientID = cr.clientID, @repID = cr.representativeID, @currentSourceID = cr.sourceID, @currentCallTypeID = cr.typeID,
						@currentFeeTypeID = cr.feeTypeID, @currentAgencyID = cr.agencyID, @counselorMemberID = cr.enteredByMemberID, @currentPanelID = cr.panelID,
						@clientParentID = c.clientParentID, @repParentID = c.clientParentID, @currentLanguageID = cr.communicateLanguageID,
						@currentStatusID = cr.statusID, @currentClientStateID = c.[state], @currentRepStateID = rep.[state],
						@homePhoneE164=c.homePhoneE164, @cellPhoneE164=c.cellPhoneE164, @alternatePhoneE164=c.alternatePhoneE164,
						@repHomePhoneE164=rep.homePhoneE164, @repCellPhoneE164=rep.cellPhoneE164, @repAlternatePhoneE164=rep.alternatePhoneE164,
						@caseID = rc.caseID, @siteID = ai.siteID, @orgID = s.orgID
					FROM dbo.ref_clientReferrals AS cr
					INNER JOIN dbo.ref_referrals AS r ON r.referralID = cr.referralID
					INNER JOIN dbo.cms_applicationInstances	AS ai ON ai.applicationInstanceID = r.applicationInstanceID
					INNER JOIN dbo.sites AS s ON s.siteID = ai.siteID
					LEFT OUTER JOIN dbo.ref_clients AS c ON c.referralID = @referralID AND c.clientID = cr.clientID
					LEFT OUTER JOIN dbo.ref_clients AS rep ON rep.referralID = @referralID AND rep.clientID = cr.representativeID
					LEFT OUTER JOIN dbo.ref_cases AS rc ON rc.referralID = @referralID AND rc.clientReferralID = cr.clientReferralID
					WHERE cr.clientReferralID = @clientReferralID;

					SET @sourceID = ISNULL(@sourceID, @currentSourceID);
					SET @callTypeID = ISNULL(@callTypeID, @currentCallTypeID);
					SET @feeTypeID = ISNULL(@feeTypeID, @currentFeeTypeID);
					SET @languageID = ISNULL(@languageID, @currentLanguageID);
					SET @agencyID = ISNULL(@agencyID, @currentAgencyID);
					SET @clientStateID = ISNULL(@clientStateID, @currentClientStateID);
					SET @repStateID = ISNULL(@repStateID, @currentRepStateID);
					SET @statusID = ISNULL(@statusID, @currentStatusID);
					SET @panelID = ISNULL(@panelID, @currentPanelID);

					IF LEN(@otherAgency)> 0 BEGIN
						EXEC dbo.ref_createAgency @referralID=@referralID, @name=@otherAgency,
							@isActive=1,@createdBy=@recordedByMemberID,@agencyID=@agencyID OUTPUT;
					END

					EXEC dbo.ref_updateClientReferral
						@referralID=@referralID, @clientReferralID=@clientReferralID, @clientID=@clientID, 
						@firstName=@firstName, @middleName=@middleName, @lastName=@lastName, 
						@businessName=@businessName, @address1=@address1, @address2=@address2,
						@city=@city, @state=@clientStateID, @postalCode=@postalCode, @countryID=NULL, @email=@email,
						@homePhone=@homePhone, @cellPhone=@cellPhone, @alternatePhone=@alternatePhone,
						@homePhoneE164=@homePhoneE164, @cellPhoneE164=@cellPhoneE164, @alternatePhoneE164=@alternatePhoneE164,
						@clientParentID=@clientParentID, @repID=@repID,
						@repFirstName=@repFirstName, @repLastName=@repLastName, @repAddress1=@repAddress1,
						@repAddress2=@repAddress2, @repCity=@repCity, @repState=@repStateID,
						@repPostalCode=@repPostalCode, @repEmail=@repEmail,
						@repHomePhone=@repHomePhone, @repCellPhone=@repCellPhone, @repAlternatePhone=@repAlternatePhone,
						@repHomePhoneE164=@repHomePhoneE164, @repCellPhoneE164=@repCellPhoneE164, @repAlternatePhoneE164=@repAlternatePhoneE164,
						@relationToClient=@relationToClient, @repParentID=@repParentID, @sourceID=@sourceID,
						@otherSource=@otherSource, @communicateLanguageID=@languageID, 
						@issueDesc=@issueDesc, @agencyID=@agencyID, @caseFeeTypeID=@feeTypeID,
						@callTypeID=@callTypeID, @sendSurvey=@sendSurvey, @sendNewsBlog=@sendNewsBlog,
						@statusID=@statusID, @panelID=@panelID, @counselorMemberID=@counselorMemberID,
						@updateMode='API', @enteredByMemberID=@recordedByMemberID;

					IF ISNULL(@caseID,0) > 0 BEGIN
						SELECT @currentCaseFees = caseFees
						FROM dbo.ref_cases
						WHERE caseID = @caseID;

						UPDATE dbo.ref_cases
						SET caseFees = @caseFees,
							dateLastUpdated = @nowDate
						WHERE caseID = @caseID;

						IF @@ROWCOUNT > 0 AND ISNULL(@caseFees,0) <> ISNULL(@currentCaseFees,0) BEGIN
							SET @message = 'Case Fees Reported by Client changed from ' + cast(@currentCaseFees as varchar(20)) + ' to ' + cast(@caseFees as varchar(20));
							EXEC dbo.ref_insertReferralUpdateHistory @orgID=@orgID, @siteID=@siteID, @clientReferralID=@clientReferralID,
								@message=@message, @enteredByMemberID=@recordedByMemberID;
						END
					END

				END TRY
				BEGIN CATCH
					IF @@trancount > 0 ROLLBACK TRANSACTION;
					EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
				END CATCH
				", 
				{
					referralID: {value: arguments.referralID, cfsqltype: 'cf_sql_integer'},
					clientReferralID: {value: arguments.clientReferralID, cfsqltype: 'cf_sql_integer'},
					firstName: {value: arguments.strReferral.client.clientfirstname, cfsqltype: 'cf_sql_varchar'},
					middleName: {value: arguments.strReferral.client.clientmiddlename, cfsqltype: 'cf_sql_varchar'},
					lastName: {value: arguments.strReferral.client.clientlastname, cfsqltype: 'cf_sql_varchar'},
					businessName: {value: arguments.strReferral.client.clientcompany, cfsqltype: 'cf_sql_varchar'},
					address1: {value: arguments.strReferral.client.clientaddress1, cfsqltype: 'cf_sql_varchar'},
					address2: {value: arguments.strReferral.client.clientaddress2, cfsqltype: 'cf_sql_varchar'},
					city: {value: arguments.strReferral.client.clientcity, cfsqltype: 'cf_sql_varchar'},
					clientStateID: {value: structKeyExists(arguments.strReferral.extData, "clientStateID") ? arguments.strReferral.extData.clientStateID : 0, cfsqltype: 'cf_sql_integer'},
					postalCode: {value: arguments.strReferral.client.clientpostalcode, cfsqltype: 'cf_sql_varchar'},
					email: {value: arguments.strReferral.client.clientemail, cfsqltype: 'cf_sql_varchar'},
					homePhone: {value: arguments.strReferral.client.clientphonehome, cfsqltype: 'cf_sql_varchar'},
					cellPhone: {value: arguments.strReferral.client.clientphonecell, cfsqltype: 'cf_sql_varchar'},
					alternatePhone: {value: arguments.strReferral.client.clientphonealternate, cfsqltype: 'cf_sql_varchar'},
					repFirstName: {value: arguments.strReferral.representative.repfirstname, cfsqltype: 'cf_sql_varchar'},
					repLastName: {value: arguments.strReferral.representative.replastname, cfsqltype: 'cf_sql_varchar'},
					repAddress1: {value: arguments.strReferral.representative.repaddress1, cfsqltype: 'cf_sql_varchar'},
					repAddress2: {value: arguments.strReferral.representative.repaddress2, cfsqltype: 'cf_sql_varchar'},
					repCity: {value: arguments.strReferral.representative.repcity, cfsqltype: 'cf_sql_varchar'},
					repStateID: {value: structKeyExists(arguments.strReferral.extData, "repStateID") ? arguments.strReferral.extData.repStateID : 0, cfsqltype: 'cf_sql_integer'},
					repPostalCode: {value: arguments.strReferral.representative.reppostalcode, cfsqltype: 'cf_sql_varchar'},
					repEmail: {value: arguments.strReferral.representative.repemail, cfsqltype: 'cf_sql_varchar'},
					repHomePhone: {value: arguments.strReferral.representative.repphonehome, cfsqltype: 'cf_sql_varchar'},
					repCellPhone: {value: arguments.strReferral.representative.repphonecell, cfsqltype: 'cf_sql_varchar'},
					repAlternatePhone: {value: arguments.strReferral.representative.repphonealternate, cfsqltype: 'cf_sql_varchar'},
					relationToClient: {value: arguments.strReferral.representative.relationtoclient, cfsqltype: 'cf_sql_varchar'},
					sourceID: {value: structKeyExists(arguments.strReferral.extData, "sourceID") ? arguments.strReferral.extData.sourceID : 0, cfsqltype: 'cf_sql_integer'},
					otherSource: {value: (arguments.strReferral.source eq "Other" and structKeyExists(arguments.strReferral, "othersource")) ? arguments.strReferral.othersource : "", cfsqltype: 'cf_sql_varchar'},
					languageID: {value: structKeyExists(arguments.strReferral.extData, "languageID") ? arguments.strReferral.extData.languageID : 0, cfsqltype: 'cf_sql_integer'},
					issueDesc: {value: arguments.strReferral.legalissue, cfsqltype: 'cf_sql_longvarchar'},
					callTypeID: {value: structKeyExists(arguments.strReferral.extData, "callTypeID") ? arguments.strReferral.extData.callTypeID : 0, cfsqltype: 'cf_sql_integer'},
					agencyID: {value: structKeyExists(arguments.strReferral.extData, "agencyID") ? arguments.strReferral.extData.agencyID : 0, cfsqltype: 'cf_sql_integer'},
					otherAgency: {value: structKeyExists(arguments.strReferral.extData, "otheragency") ? arguments.strReferral.extData.otheragency : "", cfsqltype: 'cf_sql_varchar'},
					sendSurvey: {value: arguments.strReferral.agreesurvey, cfsqltype: 'cf_sql_bit'},
					sendNewsBlog: {value: arguments.strReferral.agreenewsletter, cfsqltype: 'cf_sql_bit'},
					feeTypeID: {value: structKeyExists(arguments.strReferral.extData, "feeTypeID") ? arguments.strReferral.extData.feeTypeID : 0, cfsqltype: 'cf_sql_integer'},
					statusID: {value: structKeyExists(arguments.strReferral.extData, "statusID") ? arguments.strReferral.extData.statusID : 0, cfsqltype: 'cf_sql_integer'},
					caseFees: {value: arguments.strReferral.caseexists ? val(abs(rereplace(arguments.strReferral.case.feesreportedbyclient,'[^\d.]+','','ALL'))) : 0, cfsqltype: "cf_sql_decimal", scale: 2},
					panelID: {value: structKeyExists(arguments.strReferral.extData, "panelid1") and val(arguments.strReferral.extData.panelid1) ? arguments.strReferral.extData.panelid1 : 0, cfsqltype: 'cf_sql_integer'},
					recordedByMemberID: {value: arguments.recordedbymemberid, cfsqltype: 'cf_sql_integer'}
				},
				{ datasource="membercentral" }
			);

			/* save panel filters if any */
			var validPanelFilterKeys = "panelid1,panelid2,panelid3,subpanelid1,subpanelid2,subpanelid3";
			var strPanelFilters = StructFilter(arguments.strReferral.extData,function(key,value){
				return listFindNoCase(validPanelFilterKeys, key);
			});

			var strAllFilters = structNew();
			structAppend(strAllFilters, strPanelFilters);
			if(structKeyExists(arguments.strReferral.extData,"strFilterCF"))
				structAppend(strAllFilters, arguments.strReferral.extData.strFilterCF);
			if(structKeyExists(arguments.strReferral.extData,"strFilterClassifications"))
				structAppend(strAllFilters, arguments.strReferral.extData.strFilterClassifications);

			if(structCount(strAllFilters)){
				var qryReferralHistory = queryExecute("
					SET NOCOUNT ON;
					SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

					DECLARE @clientID int;

					SELECT @clientID = clientID
					FROM dbo.ref_clientReferrals
					WHERE clientReferralID = :clientReferralID;
				
					SELECT searchXML
					FROM searchMC.dbo.tblSearchReferralHistory
					WHERE clientID = @clientID;

					SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
					", 
					{ clientReferralID = { value=arguments.clientReferralID, cfsqltype="cf_sql_integer" } },
					{ datasource="membercentral" }
				);

				var arrCurrXMLFields = [];
				var xmlSearch = '';
				xmlSearch &= '<search xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">';
				if(qryReferralHistory.recordCount and len(qryReferralHistory.searchXML)){
					var xmlObj = XmlParse(qryReferralHistory.searchXML);

					for (var i = 1; i <= arrayLen(xmlObj.search.field); i++) {
        				var field = xmlObj.search.field[i];
						if(StructKeyExists(strAllFilters, field.XmlAttributes.name)) {
							if(len(strAllFilters[field.XmlAttributes.name]))
								xmlSearch &= '<field name="' & field.XmlAttributes.name & '" description="N/A">' & strAllFilters[field.XmlAttributes.name] & '</field>';
						}
						else xmlSearch &= '<field name="' & field.XmlAttributes.name & '" description="N/A">' & field.XmlText & '</field>';

						arrayAppend(arrCurrXMLFields, field.XmlAttributes.name);
					}
				}
				// create new field nodes for filters, if xml node / tblSearchReferralHistory entry doesn't exist
				var allFilterKeysListSorted = listSort(structKeyList(strAllFilters), "text", "asc");
				for (var thisKey in listToArray(allFilterKeysListSorted)) {
					if(!ArrayContains(arrCurrXMLFields, thisKey) and len(strAllFilters[thisKey]))
						xmlSearch &= '<field name="' & thisKey & '" description="N/A">' & strAllFilters[thisKey] & '</field>';
				}
				xmlSearch &= '</search>';

				var qryUpdate = queryExecute("
					SET XACT_ABORT, NOCOUNT ON;
					BEGIN TRY
						DECLARE @clientID int, @siteID int, @panelID1 int = :panelID1, @currentPanelID1 int,
							@subPanelID1 varchar(500) = :subPanelID1, @currentSubPanelID1 varchar(500), @xmlSearch xml = :xmlSearch,
							@recordedByMemberID int = :recordedByMemberID, @searchID int;

						SELECT @siteID = ai.siteID, @clientID = cr.clientID
						FROM dbo.ref_clientReferrals AS cr
						INNER JOIN dbo.ref_referrals AS r ON r.referralID = cr.referralID
						INNER JOIN dbo.cms_applicationInstances	AS ai ON ai.applicationInstanceID = r.applicationInstanceID
						WHERE clientReferralID = :clientReferralID;

						SELECT @currentPanelID1 = panelID1, @currentSubPanelID1 = subPanelID1
						FROM searchMC.dbo.tblSearchReferralHistory
						WHERE clientID = @clientID;

						SET @panelID1 = ISNULL(@panelID1, @currentPanelID1);
						SET @subPanelID1 = ISNULL(@subPanelID1, @currentSubPanelID1);

						EXEC dbo.ref_saveClientReferralFilterXML
							@siteID=@siteID, @clientID=@clientID, @panelID1=@panelID1, @subPanelID1=@subPanelID1,
							@xmlSearch=@xmlSearch, @statsSessionID=NULL, @enteredByMemberID=@recordedByMemberID,
							@searchID=@searchID OUTPUT;

					END TRY
					BEGIN CATCH
						IF @@trancount > 0 ROLLBACK TRANSACTION;
						EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
					END CATCH
					", 
					{
						clientReferralID: {value: arguments.clientReferralID, cfsqltype: 'cf_sql_integer'},
						panelID1: {value: structKeyExists(strPanelFilters, "panelid1") ? strPanelFilters.panelid1 : 0, cfsqltype: 'cf_sql_integer'},
						subPanelID1: {value: structKeyExists(strPanelFilters, "subpanelid1") ? strPanelFilters.subpanelid1 : 0, cfsqltype: 'cf_sql_varchar'},
						xmlSearch: {value: xmlSearch, cfsqltype: 'cf_sql_longvarchar'},
						recordedByMemberID: {value: arguments.recordedbymemberid, cfsqltype: 'cf_sql_integer'}
					},
					{ datasource="membercentral" }
				);
			}

			/* save attorneycustomfields & clientcustomfields if any */
			if(structKeyExists(arguments.strReferral.extData,"refCustomFieldsSQL") and len(arguments.strReferral.extData.refCustomFieldsSQL)){
				savecontent variable="updateSQLString" {
					writeOutput("
						SET XACT_ABORT, NOCOUNT ON;
						BEGIN TRY

							DECLARE @clientReferralID int = :clientReferralID, @valueID int, @fieldID int, @detail varchar(max), @dataID int;
							
							BEGIN TRAN;
								#preserveSingleQuotes(arguments.strReferral.extData.refCustomFieldsSQL)#
							COMMIT TRAN

						END TRY
						BEGIN CATCH
							IF @@trancount > 0 ROLLBACK TRANSACTION;
							EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
						END CATCH
					");
				};

				var sqlParams = {
					clientReferralID = { value=arguments.clientReferralID, cfsqltype="CF_SQL_INTEGER" }
				};
				var qryUpdateReferralCF = queryExecute(updateSQLString, sqlParams, { datasource="membercentral" } );

				if (structKeyExists(arguments.strReferral.extData, "arrRefFieldChanges") and arrayLen(arguments.strReferral.extData.arrRefFieldChanges)){
					local.strRequest = {
						"c": "historyEntries_SYS_ADMIN_REFERRALUPDATE",
						"d": {
							"HISTORYCODE": "SYS_ADMIN_REFERRALUPDATE",
							"ORGID": arguments.orgID,
							"ACTORMEMBERID": arguments.recordedbymemberid,
							"CLIENTREFERRALID": arguments.clientReferralID,
							"MAINMESSAGE": "Custom Field Values Changed (API)",
							"CHANGES": arguments.strReferral.extData.arrRefFieldChanges
						}
					};

					var qryAddReferralHistory = queryExecute("
						SET NOCOUNT ON;
						SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

						INSERT INTO platformQueue.dbo.queue_mongo (msgjson) 
						VALUES (:msgjson);

						SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
						", 
						{ msgjson = { value=serializeJSON(local.strRequest), cfsqltype="CF_SQL_LONGVARCHAR" } },
						{ datasource="membercentral" }
					);
				}
			}

			/* status change */
			var newStatusID = structKeyExists(arguments.strReferral.extData, "statusID") ? arguments.strReferral.extData.statusID : 0;
			if(newStatusID gt 0 and newStatusID neq prevStatusID){
				var qryStatusChange = queryExecute("
					SET XACT_ABORT, NOCOUNT ON;
					BEGIN TRY
						DECLARE @siteID int, @orgID int, @referralID int = :referralID, @clientReferralID int = :clientReferralID,
							@recordedByMemberID int = NULLIF(:recordedByMemberID, 0), @panelID int, @caseID int,
							@feeStructureID int, @nowDate datetime = getdate(),
							@newStatusID int = :newStatusID, @prevStatusID int = :prevStatusID,
							@newStatusName varchar(255), @prevStatusName varchar(255), @msgjson varchar(max);

						IF @newStatusID > 0 AND @newStatusID <> @prevStatusID BEGIN
							SELECT @caseID = rc.caseID, @panelID = ISNULL(cr.panelID,0), @siteID = ai.siteID, @orgID = s.orgID
							FROM dbo.ref_clientReferrals AS cr
							INNER JOIN dbo.ref_referrals AS r ON r.referralID = cr.referralID
							INNER JOIN dbo.cms_applicationInstances	AS ai ON ai.applicationInstanceID = r.applicationInstanceID
							INNER JOIN dbo.sites AS s ON s.siteID = ai.siteID
							LEFT OUTER JOIN dbo.ref_cases AS rc ON rc.referralID = @referralID AND rc.clientReferralID = cr.clientReferralID
							WHERE cr.clientReferralID = @clientReferralID;

							SELECT @newStatusName = statusName FROM dbo.ref_clientReferralStatus WHERE clientReferralStatusID = @newStatusID;
							SELECT @prevStatusName = statusName FROM dbo.ref_clientReferralStatus WHERE clientReferralStatusID = @prevStatusID;

							SET @msgjson = '[{ ""ITEM"":""Status"", ""NEWVALUE"":""'+ replace(@newStatusName,'""','\\""') +'"", ""OLDVALUE"":""'+ replace(@prevStatusName,'""','\\""') +'"" }]';

							EXEC dbo.ref_insertReferralUpdateHistory @orgID=@orgID, @siteID=@siteID, @clientReferralID=@clientReferralID,
								@message='Referral Status Changed (API)', @changesArray=@msgjson, @enteredByMemberID=@recordedByMemberID;
							
							IF ISNULL(@caseID,0) = 0 AND EXISTS (
								SELECT 1
								FROM dbo.ref_clientReferralStatus
								WHERE referralID = @referralID
								AND clientReferralStatusID = @newStatusID
								AND isRetainedCase = 1
								AND isClosed = 0
							) BEGIN
								SELECT TOP 1 @feeStructureID = l.feeStructureID
								FROM dbo.ref_feeStructureLevels as l 
								INNER JOIN dbo.ref_feeStructures as fs on fs.feeStructureID = l.feeStructureID
									AND fs.[status] = 'A'
									AND fs.panelID = @panelID;
								
								IF ISNULL(@feeStructureID,0) = 0 BEGIN
									SELECT TOP 1 @feeStructureID = l.feeStructureID
									FROM dbo.ref_feeStructureLevels as l 
									INNER JOIN dbo.ref_feeStructures as fs on fs.feeStructureID = l.feeStructureID
										AND fs.[status] = 'A'
										AND fs.referralID = @referralID
										AND fs.panelID IS NULL;
								END

								INSERT INTO dbo.ref_cases (referralID, clientReferralID, enteredByMemberID, dateCaseOpened, dateCreated, feeStructureID)
								VALUES (@referralID, @clientReferralID, @recordedByMemberID, @nowDate, @nowDate, @feeStructureID);
							END
						END

					END TRY
					BEGIN CATCH
						IF @@trancount > 0 ROLLBACK TRANSACTION;
						EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
					END CATCH
					", 
					{
						referralID: {value: arguments.referralID, cfsqltype: 'cf_sql_integer'},
						clientReferralID: {value: arguments.clientReferralID, cfsqltype: 'cf_sql_integer'},
						newStatusID: {value: newStatusID, cfsqltype: 'cf_sql_integer'},
						prevStatusID: {value: prevStatusID, cfsqltype: 'cf_sql_integer'},
						recordedByMemberID: {value: arguments.recordedbymemberid, cfsqltype: 'cf_sql_integer'}
					},
					{ datasource="membercentral" }
				);
			}

			// reprocess conditions
			reprocessConditions(referralID=arguments.referralID, clientReferralID=arguments.clientReferralID);

		} catch (any e) {
			strResult.success = false;
		}

		return strResult;
	}

	package numeric function getReferralID (required numeric siteID) {
		var qryResult = queryExecute("
			SET NOCOUNT ON;
			SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

			DECLARE @siteID INT = :siteID;

			SELECT TOP 1 r.referralID
			FROM dbo.ref_referrals r
			INNER JOIN dbo.cms_applicationInstances ai on ai.siteID = @siteID
				AND ai.applicationInstanceID = r.applicationInstanceID
			INNER JOIN dbo.cms_siteResources sr on sr.siteID = @siteID
				AND sr.siteResourceID = ai.siteResourceID 
				AND sr.siteResourceStatusID = 1;

			SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
			", 
			{ 
				siteID = { value=arguments.siteID, cfsqltype="cf_sql_integer" }
			},
			{ datasource="membercentral" }
		);

		return val(qryResult.referralID);
	}

	package numeric function getClientReferralIDByUID (required numeric referralID, required string clientReferralUID) {
		var qryLookupClientReferralID = queryExecute("
			SET NOCOUNT ON;
			SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

			SELECT top 1 clientReferralID
			FROM dbo.ref_clientReferrals
			WHERE referralID = :referralid
			and uid = :clientreferraluid ;

			SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
			", 
			{ 
				referralid = { value=arguments.referralID, cfsqltype="cf_sql_integer" },
				clientreferraluid = { value=arguments.clientReferralUID, cfsqltype="CF_SQL_IDSTAMP" }
			},
			{ datasource="membercentral" }
		);

		return val(qryLookupClientReferralID.clientReferralID);
	}

	package void function reprocessConditions (required numeric referralID, required numeric clientReferralID) {
		var qryReprocess = queryExecute("
			SET XACT_ABORT, NOCOUNT ON;
			BEGIN TRY
				SET NOCOUNT ON;
				SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

				IF OBJECT_ID('tempdb..##tblMCQRun') IS NOT NULL
					DROP TABLE ##tblMCQRun;
				CREATE TABLE ##tblMCQRun (orgID int, memberID int INDEX IX_tblMCQRun_memberID, conditionID int);

				DECLARE @orgID int, @referralID int = :referralID;

				SELECT @orgID = s.orgID
				FROM dbo.ref_referrals AS r
				INNER JOIN dbo.cms_applicationInstances	AS ai ON ai.applicationInstanceID = r.applicationInstanceID
				INNER JOIN dbo.sites AS s ON s.siteID = ai.siteID
				WHERE r.referralID = @referralID;

				INSERT INTO ##tblMCQRun (orgID, memberID, conditionID)
				SELECT vgc.orgID, cr.memberID, vgc.conditionID
				FROM dbo.ref_clientReferrals AS cr
				INNER JOIN dbo.ref_clients AS c ON c.referralID = @referralID and c.clientID = cr.clientID
				INNER JOIN dbo.ref_clientTypes AS ct ON ct.clientTypeID = c.typeID
					AND ct.clientType = 'Client'
				INNER JOIN dbo.ref_clientReferralTypes AS crt ON crt.clientReferralTypeID = cr.typeID
					AND crt.referralID = c.referralID
					AND crt.isReferral = 1
				INNER JOIN dbo.ams_virtualGroupConditions AS vgc ON vgc.orgID = @orgID
					AND vgc.fieldCode = 'ref_entry'
				WHERE cr.referralID = @referralID
				AND cr.clientReferralID = :clientReferralID
				AND cr.memberID IS NOT NULL;

				SET TRANSACTION ISOLATION LEVEL READ COMMITTED;

				EXEC platformQueue.dbo.queue_triggerMCGCache @runImmediately=0, @type='ConditionsAndGroupsChanged';

				IF OBJECT_ID('tempdb..##tblMCQRun') IS NOT NULL
					DROP TABLE ##tblMCQRun;

			END TRY
			BEGIN CATCH
				IF @@trancount > 0 ROLLBACK TRANSACTION;
				EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
			END CATCH
			", 
			{
				referralID = { value=arguments.referralID, cfsqltype="cf_sql_integer" },
				clientReferralID = {value=arguments.clientReferralID, cfsqltype="cf_sql_integer"},
			},
			{ datasource="membercentral" }
		);
	}

	private query function getClientFees (required numeric clientReferralID, required numeric clientID) {
		var qryPaymentData = queryExecute("
			SET NOCOUNT ON;
			SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

			DECLARE @clientID int = :clientID, @clientReferralID int = :clientReferralID, @orgID int, @referralID int;
			DECLARE @t_Tax int = dbo.fn_tr_getTypeID('Sales Tax');
			DECLARE @tr_SalesTaxTrans int = dbo.fn_tr_getRelationshipTypeID('SalesTaxTrans');

			select @clientID = coalesce(clientParentID, clientID), @referralID = referralID
			from dbo.ref_clients 
			where clientID = @clientID;

			select @orgID = s.orgID
			from dbo.ref_clients as rc
			inner join dbo.ref_referrals as r on r.referralID = rc.referralID
			inner join dbo.cms_applicationInstances	as ai on ai.applicationInstanceID = r.applicationInstanceID
			inner join dbo.sites as s on s.siteID = ai.siteID
			where rc.clientID = @clientID;

			WITH ordTrans as (
				select ts.transactionID as saleTID, ts.transactionID, ts.cache_amountAfterAdjustment, 
					ts.cache_amountAfterAdjustment-ts.cache_activePaymentAllocatedAmount-ts.cache_pendingPaymentAllocatedAmount as amtToBePaid
				from dbo.tr_applications tra
				inner join dbo.tr_transactionSales ts on ts.orgID = @orgID and ts.transactionID = tra.transactionID 
				inner join dbo.tr_transactions t on t.ownedByOrgID = @orgID and t.transactionID = ts.transactionID
				inner join dbo.ref_clients c on c.referralID = @referralID 
					and c.clientID = tra.itemID
					and coalesce(c.clientParentID,c.clientID) = @clientID
				inner join dbo.ref_clientreferrals cr on cr.referralID = @referralID 
					and cr.clientid = c.clientid 
					and t.assignedToMemberID <> cr.memberid 
					and cr.clientreferralID = @clientReferralID
				where tra.orgID = @orgID
				and tra.itemType = 'ClientReferralFee'
				and tra.status = 'A'
					union all
				select rt.transactionID as saleTID, t.transactionID, ts.cache_amountAfterAdjustment,
					ts.cache_amountAfterAdjustment-ts.cache_activePaymentAllocatedAmount-ts.cache_pendingPaymentAllocatedAmount as amtToBePaid
				from dbo.tr_transactions as t
				inner join dbo.tr_relationships as tr on tr.orgID = @orgID and tr.typeID = @tr_SalesTaxTrans and tr.transactionID = t.transactionID
				inner join dbo.tr_transactionSales as ts on ts.orgID = @orgID and ts.transactionID = t.transactionID
				inner join ordTrans as rt on rt.transactionID = tr.appliedToTransactionID
				where t.ownedByOrgID = @orgID 
				and t.typeID = @t_Tax
			)
			select distinct ot.saleTID, ot.referralDues, ot.amtToBePaid, (ot.referralDues - ot.amtToBePaid) as paidToDate,
				ts.saleID, ts.transactionID, t.transactionDate
			from dbo.tr_applications tra
			inner join dbo.tr_transactionSales ts on ts.orgID = @orgID and ts.transactionID = tra.transactionID
			inner join dbo.tr_transactions t on t.ownedByOrgID = @orgID and t.transactionID = ts.transactionID
			inner join dbo.ref_clients c on c.referralID = @referralID 
				and c.clientID = tra.itemID
				and coalesce(c.clientParentID,c.clientID) = @clientID
			inner join dbo.ref_clientreferrals cr on cr.referralID = @referralID
				and cr.clientid = c.clientid 
				and cr.clientreferralID = @clientReferralID
			inner join (
				select saleTID, sum(cache_amountAfterAdjustment) as referralDues, sum(amtToBePaid) as amtToBePaid
				from ordTrans
				group by saleTID
			) ot on ot.saleTID = tra.transactionID
			inner join dbo.ams_members m on m.memberID = t.recordedByMemberID
			where tra.orgID = @orgID 
			and ts.cache_amountAfterAdjustment >= 0;

			SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
			", 
			{ 
				clientID = { value=arguments.clientID, cfsqltype="cf_sql_integer" },
				clientReferralID = { value=arguments.clientReferralID, cfsqltype="cf_sql_integer" }
			},
			{ datasource="membercentral" }
		);

		return qryPaymentData;
	}

	private query function getConsultationFees (required numeric clientReferralID, required numeric orgID, required numeric memberID) {
		var qryPaymentData = queryExecute("
			SET NOCOUNT ON;
			SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

			DECLARE @t_Tax int = dbo.fn_tr_getTypeID('Sales Tax');
			DECLARE @tr_SalesTaxTrans int = dbo.fn_tr_getRelationshipTypeID('SalesTaxTrans');
			DECLARE @clientReferralID int = :clientReferralID, @orgID int = :orgID, @memberID int = :memberID, @referralID int;

			SELECT @referralID = referralID
			FROM dbo.ref_clientReferrals
			WHERE clientReferralID = @clientReferralID;

			WITH ordTrans as (
				select ts.transactionID as saleTID, ts.transactionID, ts.cache_amountAfterAdjustment, 
					ts.cache_amountAfterAdjustment-ts.cache_activePaymentAllocatedAmount-ts.cache_pendingPaymentAllocatedAmount as amtToBePaid
				from dbo.tr_applications tra
				inner join dbo.tr_transactionSales ts on ts.orgID = @orgID and ts.transactionID = tra.transactionID 
				inner join dbo.tr_transactions t on t.ownedByOrgID = @orgID and t.transactionID = ts.transactionID					
				inner join dbo.ref_clients c on c.referralID = @referralID and tra.itemID = coalesce(c.clientParentID,c.clientID)
				inner join dbo.ref_clientReferrals cr on cr.referralID = @referralID and cr.clientID = c.clientID
					and cr.clientReferralID = @clientReferralID
				where tra.orgID = @orgID
				and tra.itemType = 'ClientReferralFee'
				and tra.status = 'A'
				and t.assignedToMemberID = @memberID
					union all
				select rt.transactionID as saleTID, t.transactionID, ts.cache_amountAfterAdjustment, 
					ts.cache_amountAfterAdjustment-ts.cache_activePaymentAllocatedAmount-ts.cache_pendingPaymentAllocatedAmount as amtToBePaid
				from dbo.tr_transactions as t
				inner join dbo.tr_relationships as tr on tr.orgID = @orgID and tr.typeID = @tr_SalesTaxTrans and tr.transactionID = t.transactionID
				inner join dbo.tr_transactionSales ts on ts.orgID = @orgID and ts.transactionID = t.transactionID
				inner join ordTrans as rt on rt.transactionID = tr.appliedToTransactionID
				where t.ownedByOrgID = @orgID
				and t.typeID = @t_Tax
			)
			select tra.itemId, ot.saleTID, ot.referralDues, ot.amtToBePaid, (ot.referralDues - ot.amtToBePaid) as paidToDate, t.transactionDate
			from dbo.tr_applications tra
			inner join dbo.tr_transactionSales ts on ts.orgID = @orgID and ts.transactionID = tra.transactionID 
			inner join dbo.tr_transactions t on t.ownedByOrgID = @orgID and t.transactionID = ts.transactionID
			inner join dbo.tr_invoiceTransactions as it on it.orgID = @orgid and it.transactionID = ts.transactionID
			inner join dbo.tr_invoices as i on i.orgID = @orgid and i.invoiceID = it.invoiceID
			inner join dbo.organizations as o on o.orgID = @orgID
			inner join dbo.ref_clients c on c.referralID = @referralID and tra.itemID = coalesce(c.clientParentID,c.clientID)
			inner join dbo.ref_clientReferrals cr on cr.referralID = @referralID and cr.clientID = c.clientID
				and cr.clientReferralID = @clientReferralID
			inner join (
				select saleTID, sum(cache_amountAfterAdjustment) as referralDues, sum(amtToBePaid) as amtToBePaid
				from ordTrans
				group by saleTID
			) ot on ot.saleTID = tra.transactionID
			where tra.orgID = @orgID 
			and t.assignedToMemberID = @memberID
			and ts.cache_amountAfterAdjustment >= 0;

			SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
			", 
			{
				clientReferralID = { value=arguments.clientReferralID, cfsqltype="cf_sql_integer" },
				orgID = { value=arguments.orgID, cfsqltype="cf_sql_integer" },
				memberID = { value=arguments.memberID, cfsqltype="cf_sql_integer" }
			},
			{ datasource="membercentral" }
		);

		return qryPaymentData;
	}

	private query function getFeesTotals (required query qryItems) {
		var qryTotals = new Query(
			sql= "SELECT SUM(referralDues) AS referralDuesTotal, SUM(amtToBePaid) AS amtToBePaidTotal, SUM(paidToDate) AS paidToDateTotal FROM qryItems",
			dbtype= "query",
			qryItems = arguments.qryItems
		);
		var qryTotalsRes = qryTotals.execute().getResult();

		return qryTotalsRes;
	}

	private array function getReferralNotes (required numeric referralID, required numeric clientReferralID, required string noteType) {
		var arrResult = queryExecute("
			SET NOCOUNT ON;
			SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

			SELECT n.referralNote as description, format(n.createdDate, 'MM/dd/yyyy h:mm tt') as createddate, CONVERT(VARCHAR, n.followUpDate, 101) as followupdate,
				m.firstName + isnull(' ' + left(nullif(m.middleName,''),1),'') + ' ' + m.lastName as enteredby,
				case n.followUpStatus when 'P' then 'Pending' when 'C' then 'Completed' else '' end as followupstatus
			FROM dbo.ref_notes as n
			INNER JOIN dbo.ref_clientReferrals as cr on cr.referralID = :referralID
				and cr.clientReferralID = n.clientReferralID
			OUTER APPLY (
				select m3.firstName, m3.middleName, m3.lastName 
				from dbo.ams_members as m2
				inner join dbo.ams_members as m3 on m3.memberID = m2.activeMemberID
				where m2.memberID = n.createdBy	
				) as m	
			WHERE n.clientReferralID = :clientReferralID
			AND n.noteType = :noteType
			ORDER BY n.createdDate DESC;

			SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
			", 
			{
				clientReferralID = { value=arguments.clientReferralID, cfsqltype="cf_sql_integer" },
				referralID = { value=arguments.referralID, cfsqltype="cf_sql_integer" },
				noteType = { value=arguments.noteType, cfsqltype="cf_sql_varchar" }
			},
			{ datasource="membercentral", returntype="array" }
		);

		return arrResult;
	}

	private query function getCaseFees (required numeric referralID, required numeric orgID, required numeric caseID) {
		var qryPaymentData = queryExecute("
			SET NOCOUNT ON;
			SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

			DECLARE @referralID int = :referralID, @orgID int = :orgID, @caseID int = :caseID;
			DECLARE @t_Tax int = dbo.fn_tr_getTypeID('Sales Tax');
			DECLARE @tr_SalesTaxTrans int = dbo.fn_tr_getRelationshipTypeID('SalesTaxTrans');

			WITH ordTrans as (
				select ts.transactionID as saleTID, ts.transactionID, ts.cache_amountAfterAdjustment,
					ts.cache_amountAfterAdjustment-ts.cache_activePaymentAllocatedAmount-ts.cache_pendingPaymentAllocatedAmount as amtToBePaid
				from dbo.tr_applications tra
				inner join dbo.tr_transactionSales ts on ts.orgID = @orgID and ts.transactionID = tra.transactionID
				inner join dbo.tr_transactions t on t.ownedByOrgID = @orgID and t.transactionID = ts.transactionID
				inner join dbo.ref_collectedFees cf on cf.referralID = @referralID and cf.collectedFeeID = tra.itemID
				and cf.caseID = @caseID
				where tra.orgID = @orgID
				and tra.itemType = 'referralfee'
				and tra.status = 'A'
					union all
				select rt.transactionID as saleTID, t.transactionID, ts.cache_amountAfterAdjustment, 
					ts.cache_amountAfterAdjustment-ts.cache_activePaymentAllocatedAmount-ts.cache_pendingPaymentAllocatedAmount as amtToBePaid
				from dbo.tr_transactions as t
				inner join dbo.tr_relationships as tr on tr.orgID = @orgID and tr.typeID = @tr_SalesTaxTrans and tr.transactionID = t.transactionID
				inner join dbo.tr_transactionSales ts on ts.orgID = @orgID and ts.transactionID = t.transactionID
				inner join ordTrans as rt on rt.transactionID = tr.appliedToTransactionID
				where t.ownedByOrgID = @orgID
				and t.typeID = @t_Tax
			)
			select
				ot.saleTID, ot.ReferralDues, ot.amtToBePaid,
				t.assignedToMemberID,
				(m2.firstName + case when m2.middlename is not null and len(m2.middlename) > 0 then ' ' + left(m2.middleName, 1) + ' ' else ' ' end + m2.lastName) as assignedToMemberName,
				cf.collectedFeeID, cf.collectedFeeDate, cf.collectedFee, cf.filingFee
			from dbo.tr_applications tra
			inner join dbo.tr_transactionSales ts on ts.orgID = @orgID and ts.transactionID = tra.transactionID
			inner join dbo.tr_transactions t on t.ownedByOrgID = @orgID and t.transactionID = ts.transactionID
			inner join dbo.ref_collectedFees cf on cf.referralID = @referralID and cf.collectedFeeID = tra.itemID
				and cf.caseID = @caseID
			inner join (
				select saleTID, sum(cache_amountAfterAdjustment) as ReferralDues, sum(amtToBePaid) as amtToBePaid
				from ordTrans
				group by saleTID
			) ot on ot.saleTID = tra.transactionID
			inner join dbo.ams_members m2 on m2.memberID = t.assignedToMemberID
			where tra.orgID = @orgID
			and ts.cache_amountAfterAdjustment >= 0
	
			union all

			select 0, cf.importedCollectedFee as ReferralDues, 0,
				0 as assignedToMemberID,
				' ' as assignedToMemberName,
				cf.collectedFeeID, cf.collectedFeeDate, cf.collectedFee, cf.filingFee
			from dbo.ref_collectedFees cf
			left outer join dbo.tr_applications tra on tra.orgID = @orgID and cf.collectedFeeID = tra.itemID
				and tra.itemType = 'referralfee'
				and tra.status = 'A'
			where cf.referralID = @referralID
			and cf.caseID = @caseID
			and tra.itemID is null
			order by cf.collectedFeeDate;

			SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
			", 
			{
				referralID = { value=arguments.referralID, cfsqltype="cf_sql_integer" },
				orgID = { value=arguments.orgID, cfsqltype="cf_sql_integer" },
				caseID = { value=arguments.caseID, cfsqltype="cf_sql_integer" }
			},
			{ datasource="membercentral" }
		);

		var qryDistinctCaseFees = new Query(
			sql= "select collectedFeeID, collectedFeeDate, collectedFee, filingFee, sum(referralDues) as referralDues, sum(amtToBePaid) as amtToBePaid, assignedToMemberID, assignedToMemberName
				from qryPaymentData
				group by collectedFeeID, collectedFeeDate, collectedFee, filingFee, assignedToMemberID, assignedToMemberName
				order by collectedFeeDate",
			dbtype= "query",
			qryPaymentData = qryPaymentData
		);
		var qryDistinctCaseFeesRes = qryDistinctCaseFees.execute().getResult();

		return qryDistinctCaseFeesRes;
	}

	private query function getCaseFeesTotals (required query qryItems) {
		var qryDistinctCaseFees = new Query(
			sql= "select collectedFeeID, collectedFee, filingFee, sum(ReferralDues) as ReferralDues, sum(amtToBePaid) as amtToBePaid
				from qryItems
				group by collectedFeeID, collectedFee, filingFee",
			dbtype= "query",
			qryItems = arguments.qryItems
		);
		var qryDistinctCaseFeesRes = qryDistinctCaseFees.execute().getResult();

		var qryCaseFeeTotals = new Query(
			sql= "select sum(ReferralDues) as referralDuesTotal, sum(amtToBePaid) as amtToBePaidTotal, sum(collectedFee) as collectedFeeTotal, sum(filingFee) as filingFeeTotal
				from qryDistinctCaseFeesRes",
			dbtype= "query",
			qryDistinctCaseFeesRes = qryDistinctCaseFeesRes
		);
		var qryCaseFeeTotalsRes = qryCaseFeeTotals.execute().getResult();

		return qryCaseFeeTotalsRes;
	}

	private array function getFeeDiscrepancyStatusChangeLog (required numeric clientReferralID) {
		var qryStatusLog = queryExecute("
			SET NOCOUNT ON;
			SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

			SELECT s.statusName as status, sl.statusNotes as notes, CONVERT(varchar, sl.dateCreated, 101) as [date], m2.firstName + ' ' + m2.lastname as enteredby
			FROM dbo.ref_feeDiscrepancyStatusChangeLog as sl
			INNER JOIN dbo.ref_feeDiscrepancyStatuses as s on s.feeDiscrepancyStatusID = sl.feeDiscrepancyStatusID and s.isActive = 1
			INNER JOIN dbo.ams_members m on m.memberid = sl.enteredByMemberID
			INNER JOIN dbo.ams_members m2 on m2.memberid = m.activememberID
			WHERE sl.clientReferralID = :clientReferralID
			ORDER BY sl.dateCreated DESC;

			SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
			", 
			{
				clientReferralID = { value=arguments.clientReferralID, cfsqltype="cf_sql_integer" }
			},
			{ datasource="membercentral", returntype="array" }
		);

		return qryStatusLog;
	}

	private struct function getFiltersData (required numeric orgID, required numeric referralID, required numeric clientID) {
		var retStruct = {
			"primarypanel":"",
			"primarysubpanel":"N/A",
			"secondarypanel":"",
			"secondarysubpanel":"N/A",
			"tertiarypanel":"",
			"tertiarysubpanel":"N/A",
			"customfields": [],
			"classifications": []
		};
		var qryFilterPanelData = queryExecute("
			SET NOCOUNT ON;
			SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

			DECLARE @tblPanelNodes table (elementID varchar(50), elementValue varchar(max), valueLabel varchar(max));

			INSERT INTO @tblPanelNodes (elementID, elementValue)
			SELECT r.value('@name','varchar(50)') as elementID,
				r.value('(text())[1]', 'varchar(max)') as elementValue
			FROM searchMC.dbo.tblSearchReferralHistory AS h
			CROSS APPLY searchXML.nodes('/search/field') AS x(r)
			WHERE h.clientID = :clientID
			AND r.value('@name','varchar(50)') IN ('panelid1','subpanelid1','panelid2','subpanelid2','panelid3','subpanelid3')
			AND LEN(ISNULL(r.value('(text())[1]', 'varchar(max)'),'')) > 0
			ORDER BY dateEntered DESC, elementID;

			WITH panelFilters AS (
				SELECT tmp.elementID, STRING_AGG(p.[name],'|') AS panelNameList
				FROM @tblPanelNodes AS tmp
				CROSS APPLY dbo.fn_IntListToTable(tmp.elementValue,',') AS tmpPanels
				INNER JOIN dbo.ref_panels AS p ON p.panelID = tmpPanels.listItem
				GROUP BY tmp.elementID
			)
			UPDATE tmp
			SET valueLabel = p.panelNameList
			FROM @tblPanelNodes AS tmp
			INNER JOIN panelFilters AS p ON p.elementID = tmp.elementID;

			SELECT elementID, valueLabel FROM @tblPanelNodes;

			SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
			", 
			{ clientID = { value=arguments.clientID, cfsqltype="cf_sql_integer" } },
			{ datasource="membercentral" }
		);

		var keyMapping = {
			"panelid1":"primarypanel",
			"subpanelid1":"primarysubpanel",
			"panelid2":"secondarypanel",
			"subpanelid2":"secondarysubpanel",
			"panelid3":"tertiarypanel",
			"subpanelid3":"tertiarysubpanel"
		};
		for (var i = 1; i <= qryFilterPanelData.recordCount; i++) {
			retStruct[keyMapping[qryFilterPanelData.elementID[i]]] = qryFilterPanelData.valueLabel[i];
		}

		var qryFilterCFData = queryExecute("
			SET NOCOUNT ON;
			SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

			SELECT r.value('@name','varchar(50)') as elementID,
				r.value('(text())[1]', 'varchar(max)') as elementValue
			FROM searchMC.dbo.tblSearchReferralHistory AS h
			CROSS APPLY searchXML.nodes('/search/field') AS x(r)
			WHERE h.clientID = :clientID
			AND r.value('@name','varchar(50)') NOT IN ('panelid1','subpanelid1','panelid2','subpanelid2','panelid3','subpanelid3')
			AND left(r.value('@name','varchar(50)'),7) <> 'mg_gid_'
			ORDER BY dateEntered DESC, elementID;

			SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
			", 
			{ clientID = { value=arguments.clientID, cfsqltype="cf_sql_integer" } },
			{ datasource="membercentral" }
		);

		var xmlFields = getMemberFieldsXMLForReferralSearch(referralID=arguments.referralID, orgID=arguments.orgID);

		var thisFieldValue;
		var thisFieldFinalVal;
		if (arrayLen(xmlFields.xmlRoot.xmlChildren)) {
			for (var currentfield in xmlFields.xmlRoot.xmlChildren) {
				thisFieldFinalVal = '';
				thisFieldValue = "";

				for (var row in qryFilterCFData) {
					if (qryFilterCFData.elementID == currentfield.xmlattributes.fieldCode) {
						thisFieldValue = qryFilterCFData.elementValue;
						break;
					}
				}

				var thisOptColValue = "";

				switch (currentfield.xmlAttributes.displayTypeCode) {
					case "TEXTBOX":
						if ((reFindNoCase('mat?_[0-9]+_postalcode', currentfield.xmlattributes.fieldCode) || findNoCase('_proximity', currentfield.xmlattributes.dbField)) && len(thisFieldValue)) {
							var thisRadiusValue = "--";
							for (var row in qryFilterCFData) {
								if (qryFilterCFData.elementID == "#currentfield.xmlattributes.fieldCode#_radius") {
									thisRadiusValue = qryFilterCFData.elementValue;
									break;
								}
							}
							for (var thisrad in [5, 10, 25, 50, 100]) {
								if (listFindNoCase(thisRadiusValue, thisrad)) {
									thisFieldFinalVal &= "Within " & thisrad & " miles of ";
									break;
								}
							}
							thisFieldFinalVal &= (len(trim(thisFieldValue)) ? thisFieldValue : "--");
						} else {
							thisFieldFinalVal = thisFieldValue;
						}
						break;

					case "RADIO":
						for (var thisOpt in currentfield.xmlChildren) {
							switch (currentfield.xmlAttributes.dataTypeCode) {
								case "STRING":
									thisOptColValue = thisOpt.xmlAttributes.columnValueString;
									break;
								case "DECIMAL2":
									thisOptColValue = thisOpt.xmlAttributes.columnValueDecimal2;
									break;
								case "INTEGER":
									thisOptColValue = thisOpt.xmlAttributes.columnValueInteger;
									break;
								case "DATE":
									thisOptColValue = dateFormat(replaceNoCase(thisOpt.xmlAttributes.columnValueDate, 'T', ' '), "mm/dd/yyyy");
									break;
								case "BIT":
									thisOptColValue = thisOpt.xmlAttributes.columnValueBit;
									break;
								default:
									thisOptColValue = "";
							}

							if (thisFieldValue eq thisOptColValue) {
								thisFieldFinalVal = (currentfield.xmlAttributes.dataTypeCode == "BIT") ? YesNoFormat(thisOptColValue) : thisOptColValue;
								break;
							}
						}
						break;

					case "SELECT":
					case "CHECKBOX":
						if (reFindNoCase('ma_[0-9]+_stateprov', currentfield.xmlattributes.fieldCode)) {
							var qryState = queryExecute(
								"SELECT stateid, [name] as stateName FROM dbo.ams_states WHERE [Code] = :stateCode;", 
								{ stateCode = { value=thisFieldValue, cfsqltype="cf_sql_varchar" }},
								{ datasource="membercentral" }
							);

							if(qryState.recordCount){
								thisFieldFinalVal = qryState.stateName;
							}
						} else if (listFindNoCase("m_recordtypeid,m_membertypeid,m_status", currentfield.xmlattributes.fieldCode)) {
							for (var thisOpt in currentfield.xmlChildren) {
								if (thisOpt.xmlAttributes.valueID == thisFieldValue) {
									thisFieldFinalVal = thisOpt.xmlAttributes.columnValueString;
									break;
								}
							}
						} else {
							var arrFieldValues = [];
							var thisOptColValue = "";
							var thisOptColDisplay = "";
							for (var thisOpt in currentfield.xmlChildren) {
								switch (currentfield.xmlAttributes.dataTypeCode) {
									case "STRING":
										thisOptColValue = thisOpt.xmlAttributes.columnValueString;
										thisOptColDisplay = thisOpt.xmlAttributes.columnValueString;
										break;
									case "DECIMAL2":
										thisOptColValue = thisOpt.xmlAttributes.columnValueDecimal2;
										thisOptColDisplay = thisOpt.xmlAttributes.columnValueDecimal2;
										break;
									case "INTEGER":
										thisOptColValue = thisOpt.xmlAttributes.columnValueInteger;
										thisOptColDisplay = thisOpt.xmlAttributes.columnValueInteger;
										break;
									case "DATE":
										thisOptColValue = dateFormat(replaceNoCase(thisOpt.xmlAttributes.columnValueDate, 'T', ' '), "mm/dd/yyyy");
										thisOptColDisplay = dateFormat(replaceNoCase(thisOpt.xmlAttributes.columnValueDate, 'T', ' '), "mm/dd/yyyy");
										break;
									case "BIT":
										thisOptColValue = thisOpt.xmlAttributes.columnValueBit;
										thisOptColDisplay = YesNoFormat(thisOpt.xmlAttributes.columnValueBit);
										break;
									default:
										thisOptColValue = "";
										thisOptColDisplay = "";
								}

								if (listFindNoCase(thisFieldValue, thisOptColValue)) {
									arrayAppend(arrFieldValues, thisOptColDisplay);
								}
							}
							thisFieldFinalVal = arrayToList(arrFieldValues,"|");
						}
						break;

					case "DATE":
						thisFieldFinalVal = len(thisFieldValue) ? dateFormat(thisFieldValue, "mm/dd/yyyy") : "";
						break;
				}

				retStruct.customfields.append({ 
					"label": currentfield.xmlattributes.fieldLabel,
					"value": thisFieldFinalVal
				});
			}
		}

		var qryClassifications = getClassifications(referralID=arguments.referralID);

		var qryFilterClassificationData = queryExecute("
			SET NOCOUNT ON;
			SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

			DECLARE @tblClassificationNodes table (elementID varchar(50), elementValue varchar(max), valueLabel varchar(max));

			INSERT INTO @tblClassificationNodes (elementID, elementValue)
			SELECT r.value('@name','varchar(50)') as elementID,
				r.value('(text())[1]', 'varchar(max)') as elementValue
			FROM searchMC.dbo.tblSearchReferralHistory AS h
			CROSS APPLY searchXML.nodes('/search/field') AS x(r)
			WHERE h.clientID = :clientID
			AND LEFT(r.value('@name','varchar(50)'),7) = 'mg_gid_'
			AND LEN(ISNULL(r.value('(text())[1]', 'varchar(max)'),'')) > 0
			ORDER BY dateEntered DESC, elementID;

			WITH classificationFilters AS (
				SELECT tmp.elementID, STRING_AGG(isnull(gsg.labelOverride,g.groupName),'|') AS groupNameList
				FROM @tblClassificationNodes AS tmp
				CROSS APPLY dbo.fn_IntListToTable(tmp.elementValue,',') AS tmpPanels
				INNER JOIN dbo.ams_memberGroupSetGroups AS gsg ON gsg.groupSetGroupID = tmpPanels.listItem
				INNER JOIN dbo.ams_groups AS g ON g.groupID = gsg.groupID
				GROUP BY tmp.elementID
			)
			UPDATE tmp
			SET valueLabel = g.groupNameList
			FROM @tblClassificationNodes AS tmp
			INNER JOIN classificationFilters AS g ON g.elementID = tmp.elementID;

			SELECT elementID, valueLabel FROM @tblClassificationNodes;

			SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
			", 
			{ clientID = { value=arguments.clientID, cfsqltype="cf_sql_integer" } },
			{ datasource="membercentral" }
		);

		for (var i = 1; i <= qryClassifications.recordCount; i++) {
			var thisFieldValue = '';
			for (var row in qryFilterClassificationData) {
				if (qryFilterClassificationData.elementID == "mg_gid_" & qryClassifications.groupSetID[i]) {
					thisFieldValue = qryFilterClassificationData.valueLabel;
					break;
				}
			}
			retStruct.classifications.append({
				"label": len(trim(qryClassifications.name[i])) ? qryClassifications.name[i] : qryClassifications.groupSetName[i],
				"value": thisFieldValue
			});
		}

		return retStruct;
	}

	private xml function getMemberFieldsXMLForReferralSearch(required numeric referralID, required numeric orgID) {
		var thisNode;
		var qryMemberFieldsXML = "";

		var qryRefSearchFieldSet = queryExecute("
			SET NOCOUNT ON;
			SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

			DECLARE @siteResourceID int;

			SELECT @siteResourceID = ai.siteResourceID
			FROM dbo.ref_referrals AS r
			INNER JOIN dbo.cms_applicationInstances AS ai ON ai.applicationInstanceID = r.applicationInstanceID
			where r.referralID = :referralID;

			SELECT TOP 1 mfu.fieldsetID
			FROM dbo.ams_memberFieldUsage AS mfu
			INNER JOIN dbo.ams_memberFieldSets mfs ON mfs.fieldsetID = mfu.fieldsetID
			WHERE mfu.siteResourceID = @siteResourceID
			AND mfu.area = 'referralsearch'
			ORDER BY mfu.fieldsetorder, mfu.fieldsetid;

			SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
			", 
			{ referralID = { value=arguments.referralID, cfsqltype="cf_sql_integer" } },
			{ datasource="membercentral" }
		);

		storedproc procedure="ams_getMemberFields" datasource="membercentral" {
			procparam cfsqltype="CF_SQL_INTEGER" type="in" value="#qryRefSearchFieldSet.fieldsetID#";
			procresult variable="qryMemberFieldsXML";
		};
		var xmlFields = XMLParse(qryMemberFieldsXML.fieldsXML);

		thisNode = xmlSearch(xmlFields, "/fields/mf[@displayTypeCode='SELECT' and (@fieldCode='m_recordtypeid')]");
		if (arrayLen(thisNode)) {
			storedproc procedure="ams_getOrgRecordTypes" datasource="membercentral" {
				procparam cfsqltype="CF_SQL_INTEGER" type="in" value="#arguments.orgID#";
				procresult variable="qryOrgRecordTypes";
			};

			if (qryOrgRecordTypes.recordcount > 1) {
				XmlDeleteNodes(xmlFields, thisNode[1].xmlChildren);
				for (var i = 1; i <= qryOrgRecordTypes.recordcount; i++) {
					var newOpt = xmlElemNew(xmlFields, "opt");
					newOpt.xmlAttributes["valueID"] = qryOrgRecordTypes.recordTypeID[i];
					newOpt.xmlAttributes["columnValueString"] = qryOrgRecordTypes.recordTypeName[i];
					arrayAppend(thisNode[1].xmlChildren, newOpt);
				}
			}
		}

		thisNode = xmlSearch(xmlFields, "/fields/mf[@displayTypeCode='SELECT' and (@fieldCode='m_status')]");
		if (arrayLen(thisNode)) {
			XmlDeleteNodes(xmlFields, thisNode[1].xmlChildren);
			
			var activeOpt = xmlElemNew(xmlFields, "opt");
			activeOpt.xmlAttributes["valueID"] = "A";
			activeOpt.xmlAttributes["columnValueString"] = "Active";
			arrayAppend(thisNode[1].xmlChildren, activeOpt);
			
			var inactiveOpt = xmlElemNew(xmlFields, "opt");
			inactiveOpt.xmlAttributes["valueID"] = "I";
			inactiveOpt.xmlAttributes["columnValueString"] = "Inactive";
			arrayAppend(thisNode[1].xmlChildren, inactiveOpt);
		}

		thisNode = xmlSearch(xmlFields, "/fields/mf[@displayTypeCode='SELECT' and (@fieldCode='m_membertypeid')]");
		if (arrayLen(thisNode)) {
			XmlDeleteNodes(xmlFields, thisNode[1].xmlChildren);
			
			var guestOpt = xmlElemNew(xmlFields, "opt");
			guestOpt.xmlAttributes["valueID"] = "1";
			guestOpt.xmlAttributes["columnValueString"] = "Guest";
			arrayAppend(thisNode[1].xmlChildren, guestOpt);
			
			var userOpt = xmlElemNew(xmlFields, "opt");
			userOpt.xmlAttributes["valueID"] = "2";
			userOpt.xmlAttributes["columnValueString"] = "User";
			arrayAppend(thisNode[1].xmlChildren, userOpt);
		}

		var arrMF = xmlSearch(xmlFields,
			"/fields/mf[@dataTypeCode='CONTENTOBJ' or @dataTypeCode='DOCUMENTOBJ' 
				or substring(@fieldCode,1,13)='acct_balance_' 
				or ( (@displayTypeCode='SELECT' or @displayTypeCode='RADIO' or @displayTypeCode='CHECKBOX') and count(opt[@valueID])=0 and not(contains(@fieldCode,'_stateprov')) )]"
		);
		XmlDeleteNodes(xmlFields, arrMF);

		return xmlFields;
	}

	private void function XmlDeleteNodes(any XmlDocument, any Nodes) {
		var Node;

		if (!isArray(arguments.Nodes)) {
			Node = arguments.Nodes;
			arguments.Nodes = [Node];
		}

		for (var NodeIndex = arrayLen(arguments.Nodes); NodeIndex >= 1; NodeIndex--) {
			Node = arguments.Nodes[NodeIndex];
			if (structKeyExists(Node, "XmlChildren")) {
				Node.XmlAttributes["delete-me-flag"] = "true";
			} else {
				arrayDeleteAt(arguments.Nodes, NodeIndex);
			}
		}

		for (Node in arguments.Nodes) {
			var ParentNodes = xmlSearch(Node, "../");
			if (arrayLen(ParentNodes) && structKeyExists(ParentNodes[1], "XmlChildren")) {
				var ParentNode = ParentNodes[1];
				for (var NodeIndex = arrayLen(ParentNode.XmlChildren); NodeIndex >= 1; NodeIndex--) {
					Node = ParentNode.XmlChildren[NodeIndex];
					if (structKeyExists(Node.XmlAttributes, "delete-me-flag")) {
						arrayDeleteAt(ParentNode.XmlChildren, NodeIndex);
						structDelete(Node.XmlAttributes, "delete-me-flag");
					}
				}
			}
		}
		return;
	}

	private query function getClassifications (required numeric referralID) {
		var qryClassifications = queryExecute("
			SET NOCOUNT ON;
			SET TRANSACTION ISOLATION LEVEL SNAPSHOT;
			
			select rc.classificationid, rc.name, mgs.groupSetName, rc.referralID, rc.showInSearchResults, rc.groupSetID, rc.classificationOrder
			from dbo.ref_classifications rc
			inner join dbo.ams_memberGroupSets mgs on mgs.groupSetID = rc.groupSetID
			where rc.referralID = :referralID
			and rc.allowSearch = 1
			order by rc.classificationOrder;

			SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
			", 
			{ referralID = { value=arguments.referralID, cfsqltype="cf_sql_integer" } },
			{ datasource="membercentral" }
		);

		return qryClassifications;
	}

	private string function getSearchXMLByClientID (required numeric clientID) {
		var qryGetSearchXML = queryExecute("
			SET NOCOUNT ON;
			SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

			DECLARE @thisClientID int = :clientID;

			SELECT @thisClientID = ISNULL(clientParentID,@thisClientID) 
			FROM dbo.ref_clients 
			WHERE clientID = @thisClientID;		

			SELECT searchXML 
			FROM searchMC.dbo.tblSearchReferralHistory 
			WHERE clientID = @thisClientID;	

			SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
			", 
			{ clientID = { value=arguments.clientID, cfsqltype="cf_sql_integer" } },
			{ datasource="membercentral" }
		);

		var questionAnswerPath = '';
		if (qryGetSearchXML.recordCount && arrayLen(XMLSearch(qryGetSearchXML.searchXML, "//field[@name='questionpath']"))) {
			questionAnswerPath = XMLSearch(qryGetSearchXML.searchXML, "//field[@name='questionpath']")[1].XmlText;
		}

		return questionAnswerPath;
	}

	public query function getFieldsXML(required numeric siteID, required string resourceType, required string areaName, required numeric csrID) {
		storedproc procedure="cf_getFieldsXML" datasource="membercentral" cachedwithin="request" {
			procparam cfsqltype="CF_SQL_INTEGER" type="in" value="#arguments.siteID#";
			procparam cfsqltype="CF_SQL_VARCHAR" type="in" value="#arguments.resourceType#";
			procparam cfsqltype="CF_SQL_VARCHAR" type="in" value="#arguments.areaName#";
			procparam cfsqltype="CF_SQL_INTEGER" type="in" value="#arguments.csrID#";
			procparam cfsqltype="CF_SQL_INTEGER" type="in" null="yes";
			procparam cfsqltype="CF_SQL_BIT" type="in" value="0";
			procresult variable="qryFieldsXML";
		};

		return qryFieldsXML;
	}

	public struct function renderResourceFields(required numeric siteID, required numeric csrID, required numeric itemID, required string resourceType, required string areaName, required string itemType) {
		var strReturn = { "fieldgroupings": [] };

		var resourceType = '';
		var areaName = '';

		var qryFieldsXML = getFieldsXML(siteID=arguments.siteID, resourceType=arguments.resourceType, areaName=arguments.areaName, csrID=arguments.csrID);
		var fieldsXML = xmlParse(qryFieldsXML.returnXML).xmlRoot;

		// check if there are fields
		var hasFields = arrayLen(fieldsXML.xmlChildren) > 0;

		if (!hasFields) {
			return strReturn;
		}

		// default grouping fields
		var arrGroupingFields = [{
			"fieldGroupingID": 0,
			"fieldGrouping": '',
			"fieldGroupingDesc": '',
			"arrFields": xmlSearch(fieldsXML, "//fields/field[@fieldGroupingID='0']")
		}];

		var qryFieldGroupings = getFieldUsageFieldGroupings(csrID=arguments.csrID, usageRT=arguments.resourceType, usageAN=arguments.areaName);

		for (var strFieldGrouping in qryFieldGroupings) {
			arrayAppend(arrGroupingFields, {
				"fieldGroupingID": strFieldGrouping.fieldGroupingID,
				"fieldGrouping": strFieldGrouping.fieldGrouping,
				"fieldGroupingDesc": strFieldGrouping.fieldGroupingDesc,
				"arrFields": xmlSearch(fieldsXML, "//fields/field[@fieldGroupingID='#strFieldGrouping.fieldGroupingID#']")
			});
		}

		var thisValue;
		var arrFieldIDs = [];
		for (var thisGrouping in arrGroupingFields) {
			thisGroupFields = [];

			for (var thisfield in thisGrouping.arrFields) {
				var thisFieldValue = "";
				var tmpAtt = thisfield.xmlattributes;
				var hideField = listFind("SELECT,RADIO,CHECKBOX", tmpAtt.displayTypeCode) && arrayLen(thisfield.xmlchildren) == 0;

				if (!hideField) {
					switch(tmpAtt.displayTypeCode) {
						case "LABEL":
							thisFieldValue = tmpAtt.fieldText;
							break;
						case "TEXTBOX":
							thisValue = getFieldResponseEntered(itemType=arguments.itemType, itemID=arguments.itemID, fieldID=tmpAtt.fieldID);
							if (tmpAtt.supportAmt == 1 && tmpAtt.supportQty == 0) {
								thisFieldValue = numberFormat(thisValue, "_$_9,999.99");
							} else {
								thisFieldValue = thisValue;
							}
							break;
						case "SELECT":
						case "RADIO":
							thisValue = getFieldOptionsSelected(itemType=arguments.itemType, itemID=arguments.itemID, fieldID=tmpAtt.fieldID);
							for (var thisoption in thisfield.xmlchildren) {
								if (thisValue == thisoption.xmlattributes.valueID) {
									thisFieldValue = thisoption.xmlattributes.fieldValue;
								}
							}
							break;
						case "CHECKBOX":
							thisValue = getFieldOptionsSelected(itemType=arguments.itemType, itemID=arguments.itemID, fieldID=tmpAtt.fieldID);
							var arrOptionDesc = [];

							for (var thisoption in thisfield.xmlchildren) {
								if (listContains(thisValue, thisoption.xmlattributes.valueID)) {
									arrayAppend(arrOptionDesc, thisoption.xmlattributes.fieldValue);
								}
							}
							if (arrayLen(arrOptionDesc)) {
								thisFieldValue = arrayToList(arrOptionDesc,"|");
							}
							break;
						case "TEXTAREA":
							thisFieldValue = getFieldResponseEntered(itemType=arguments.itemType, itemID=arguments.itemID, fieldID=tmpAtt.fieldID);
							break;
						case "DATE":
							thisValue = getFieldResponseEntered(itemType=arguments.itemType, itemID=arguments.itemID, fieldID=tmpAtt.fieldID);
							thisFieldValue = dateFormat(thisValue, 'm/d/yyyy');
							break;
						case "DOCUMENT":
							var qryThisFieldDoc = getFieldDocumentDetails(itemType=arguments.itemType, itemID=arguments.itemID, fieldID=tmpAtt.fieldID);
							if (val(strThisFieldDoc.siteResourceID) > 0) {
								thisFieldValue = strThisFieldDoc.fileName;
							}
							break;
					}
				}

				arrayAppend(thisGroupFields, { 
					"label": tmpAtt.fieldText, 
					"value": thisFieldValue ,
					"field_api_id": tmpAtt.uid
				});
			}

			arrayAppend(strReturn.fieldgroupings, {
				"groupname": thisGrouping.fieldGroupingID > 0 ? thisGrouping.fieldGrouping : "",
				"fields": thisGroupFields,
				"ungrouped": thisGrouping.fieldGroupingID == 0 ? 1 : 0
			});
		}

		return strReturn;
	}

	private query function getFieldUsageFieldGroupings (required numeric csrID, required string usageRT, required string usageAN) {
		var qryFieldUsages = queryExecute("
			SET NOCOUNT ON;
			SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

			DECLARE @usageID int, @parentUsageID int;
			SELECT @usageID = dbo.fn_cf_getUsageID(:usageRT, :usageAN, NULL);

			SELECT @parentUsageID = CASE WHEN parentUsageID IS NOT NULL THEN parentUsageID ELSE usageID END
			FROM dbo.cf_fieldUsages 
			WHERE usageID = @usageID;

			SELECT fieldGroupingID, fieldGrouping, fieldGroupingDesc, fieldGroupingOrder
			FROM dbo.cf_fieldsGrouping
			WHERE fieldControllingSiteResourceID = :csrID
			AND fieldUsageID = @parentUsageID
			ORDER BY fieldGroupingOrder;

			SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
			", 
			{
				csrID = { value=arguments.csrID, cfsqltype="cf_sql_integer" },
				usageRT = { value=arguments.usageRT, cfsqltype="cf_sql_varchar" },
				usageAN = { value=arguments.usageAN, cfsqltype="cf_sql_varchar" }
			},
			{ datasource="membercentral" }
		);

		return qryFieldUsages;
	}

	private query function getResponses (required string itemType, required numeric itemID, numeric restrictToFieldID = 0) {
		storedproc procedure="cf_getResponses" datasource="membercentral" {
			procparam cfsqltype="CF_SQL_INTEGER" type="in" value="#arguments.itemID#";
			procparam cfsqltype="CF_SQL_VARCHAR" type="in" value="#arguments.itemType#";
			if (arguments.restrictToFieldID GT 0)
				procparam cfsqltype="CF_SQL_INTEGER" type="in" value="#arguments.restrictToFieldID#";
			else
				procparam cfsqltype="CF_SQL_INTEGER" type="in" null="yes";
			procresult variable="qryFieldResponses";
		};

		return qryFieldResponses;
	}

	private string function getFieldResponseEntered (required string itemType, required numeric itemID, required numeric fieldID) {
		var qryResponseDetails = getResponses(itemType=itemType, itemID=itemID, restrictToFieldID=fieldID);
		return qryResponseDetails.customvalue;
	}

	private query function getFieldOptions (required numeric fieldID, string restrictToValueIDList = "") {
		storedproc procedure="cf_getFieldOptions" datasource="membercentral" {
			procparam cfsqltype="CF_SQL_INTEGER" type="in" value="#arguments.fieldID#";
			if (len(arguments.restrictToValueIDList))
				procparam cfsqltype="CF_SQL_LONGVARCHAR" type="in" value="#arguments.restrictToValueIDList#";
			else
				procparam cfsqltype="CF_SQL_LONGVARCHAR" type="in" null="yes";
			procresult variable="qryFieldOptions";
		};

		return qryFieldOptions;
	}

	private string function getFieldOptionsSelected (required string itemType, required numeric itemID, required numeric fieldID) {
		var qryDetails = queryExecute("
			SET NOCOUNT ON;
			SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

			SELECT fd.valueID
			FROM dbo.cf_fieldData as fd
			INNER JOIN dbo.cf_fields as f on f.fieldID = fd.fieldID
			INNER JOIN dbo.cf_fieldTypes as ft on ft.fieldTypeID = f.fieldTypeID
			INNER JOIN dbo.cf_fieldValues as fv on fv.valueID = fd.valueID and fv.fieldID = f.fieldID
			WHERE fd.fieldID = :fieldID
			AND fd.itemType = :itemType
			AND fd.itemID = :itemID;

			SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
			", 
			{
				itemType = { value=arguments.itemType, cfsqltype="cf_sql_varchar" },
				itemID = { value=arguments.itemID, cfsqltype="cf_sql_integer" },
				fieldID = { value=arguments.fieldID, cfsqltype="cf_sql_integer" }
			},
			{ datasource="membercentral" }
		);

		return valueList(qryDetails.valueID);
	}

	private query function getFieldDocumentDetails (required string itemType, required numeric itemID, required numeric fieldID) {
		var qryDetails = queryExecute("
			SET NOCOUNT ON;
			SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

			SELECT isnull(dv.filename,'') as fileName
			FROM dbo.cf_fieldData as fd
			INNER JOIN dbo.cf_fields as f on f.fieldID = fd.fieldID
			INNER JOIN dbo.cf_fieldTypes as ft on ft.fieldTypeID = f.fieldTypeID
			INNER JOIN dbo.cf_fieldValues as fv on fv.valueID = fd.valueID and fv.fieldID = f.fieldID
			INNER JOIN dbo.cms_documents as d on d.siteResourceID = fv.valueSiteResourceID
			INNER JOIN dbo.cms_documentLanguages as dl on dl.documentID = d.documentID and dl.languageID = 1
			INNER JOIN dbo.cms_documentVersions as dv on dv.documentLanguageID = dl.documentLanguageID and dv.isActive = 1
			WHERE fd.fieldID = :fieldID
			AND fd.itemType = :itemType
			AND fd.itemID = :itemID;

			SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
			", 
			{
				itemType = { value=arguments.itemType, cfsqltype="cf_sql_varchar" },
				itemID = { value=arguments.itemID, cfsqltype="cf_sql_integer" },
				fieldID = { value=arguments.fieldID, cfsqltype="cf_sql_integer" }
			},
			{ datasource="membercentral" }
		);

		return qryDetails;
	}

	private query function getClientReferralStatus (required numeric referralID, numeric statusID, boolean isClosed, boolean isClosedByLawyer, boolean isRetainedCase, 
		boolean isPending, boolean isDeleted, boolean isOpen, boolean isReferred, boolean canEditLawyer, boolean canRefer, boolean isActive, 
		boolean isAgency, boolean isConsultation, boolean isClosedNisOpen
	) {
		var qryStatus = queryExecute("
			SET NOCOUNT ON;
			SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

			SELECT clientReferralStatusID, statusName, canEditClient, canRefer, canEditFilter, canEditLawyer, cannotReopen, isReferred, isAgency, 
				isActive, isPending, isClosed, isClosedByLawyer, isOpen, isRetainedCase, isDeleted, dspFrontEnd, isConsultation, isFeeDiscrepancyAssess,
				case when isOpen = 1 AND isClosed = 0  AND isDeleted = 0  
					AND canEditClient = 1 AND canEditFilter = 0 AND canEditLawyer = 0
					AND canRefer = 0 AND isReferred = 1 AND isAgency =0 
					AND isPending =0 AND isClosedByLawyer = 0
						then 'Open'
				when isOpen = 1 AND isClosed = 0  AND isDeleted = 0  
					AND canEditClient = 1 AND canEditFilter = 1 AND canEditLawyer = 1
					AND canRefer = 1 AND isReferred = 0 AND isAgency =0 
					AND isPending =1 AND isClosedByLawyer = 0 AND isRetainedCase = 0 AND dspFrontEnd =0
					then 'Pending - Referral NOT Sent'
				when isOpen = 1 AND isClosed = 0  AND isDeleted = 0  
					AND canEditClient = 1 AND canEditFilter = 0 AND canEditLawyer = 1
					AND canRefer = 0 AND isReferred = 1 AND isAgency =0 
					AND isPending =0 AND isClosedByLawyer = 0 AND isRetainedCase = 0 AND dspFrontEnd =0
					then 'Pending - Referral Sent'
				when isOpen = 0 AND isClosed = 1  AND isDeleted = 0  
					AND canEditClient = 1 AND canEditFilter = 0 AND canEditLawyer = 0
					AND canRefer = 0 AND isAgency =0 
					AND isPending =0
					then 'Closed'
				when isOpen = 0 AND isClosed = 1  AND isDeleted = 0  
					AND canEditClient = 1 AND canEditFilter = 0 AND canEditLawyer = 0
					AND canRefer = 0 AND isAgency =1 
					AND isPending =0
					then 'Closed - Referral to Agency'
				when isDeleted = 1 then 'Deleted' end	
				as primaryStatus
			from dbo.ref_clientReferralStatus
			where referralID = :referralID
			#arguments.keyExists('statusID') ? ' AND clientReferralStatusID = #val(arguments.statusID)#' : ''#
			#arguments.keyExists('isClosed') ? ' AND isClosed = #arguments.isClosed ? 1 : 0#' : ''#
			#arguments.keyExists('isClosedByLawyer') ? ' AND isClosedByLawyer = #arguments.isClosedByLawyer ? 1 : 0#' : ''#
			#arguments.keyExists('isRetainedCase') ? ' AND isRetainedCase = #arguments.isRetainedCase ? 1 : 0#' : ''#
			#arguments.keyExists('isPending') ? ' AND isPending = #arguments.isPending ? 1 : 0#' : ''#
			#arguments.keyExists('isDeleted') ? ' AND isDeleted = #arguments.isDeleted ? 1 : 0#' : ''#
			#arguments.keyExists('isOpen') ? ' AND isOpen = #arguments.isOpen ? 1 : 0#' : ''#
			#arguments.keyExists('isReferred') ? ' AND isReferred = #arguments.isReferred ? 1 : 0#' : ''#
			#arguments.keyExists('canEditLawyer') ? ' AND canEditLawyer = #arguments.canEditLawyer ? 1 : 0#' : ''#
			#arguments.keyExists('canRefer') ? ' AND canRefer = #arguments.canRefer ? 1 : 0#' : ''#
			#arguments.keyExists('isActive') ? ' AND isActive = #arguments.isActive ? 1 : 0#' : ''#
			#arguments.keyExists('isAgency') ? ' AND isAgency = #arguments.isAgency ? 1 : 0#' : ''#
			#arguments.keyExists('isConsultation') ? ' AND isConsultation = #arguments.isConsultation ? 1 : 0#' : ''#
			#arguments.keyExists('isClosedNisOpen') ? ' AND (isClosed = 1 OR isOpen = 1)' : ''#
			ORDER BY 
			CASE 
			WHEN isOpen = 1 AND isClosed = 0 AND isDeleted = 0  
					AND canEditClient = 1 AND canEditFilter = 0 AND canEditLawyer = 0
					AND canRefer = 0 AND isReferred = 1 AND isAgency = 0 
					AND isPending = 0 AND isClosedByLawyer = 0
			THEN 'open'
			WHEN isOpen = 1 AND isClosed = 0 AND isDeleted = 0  
					AND canEditClient = 1 AND canEditFilter = 1 AND canEditLawyer = 1
					AND canRefer = 1 AND isReferred = 0 AND isAgency = 0 
					AND isPending = 1 AND isClosedByLawyer = 0 AND isRetainedCase = 0 AND dspFrontEnd = 0
			THEN 'pendingreferralnotsent'
			WHEN isOpen = 1 AND isClosed = 0 AND isDeleted = 0  
					AND canEditClient = 1 AND canEditFilter = 0 AND canEditLawyer = 1
					AND canRefer = 0 AND isReferred = 1 AND isAgency = 0 
					AND isPending = 0 AND isClosedByLawyer = 0 AND isRetainedCase = 0 AND dspFrontEnd = 0
			THEN 'pendingreferralsent'
			WHEN isOpen = 0 AND isClosed = 1 AND isDeleted = 0  
					AND canEditClient = 1 AND canEditFilter = 0 AND canEditLawyer = 0
					AND canRefer = 0 AND isAgency = 0 AND isPending = 0
			THEN 'closed'
			WHEN isOpen = 0 AND isClosed = 1 AND isDeleted = 0  
					AND canEditClient = 1 AND canEditFilter = 0 AND canEditLawyer = 0
					AND canRefer = 0 AND isAgency = 1 AND isPending = 0
			THEN 'closedreferraltoagency'
			WHEN isDeleted = 1 
			THEN 'deleted'
			END ASC,
			statusName ASC;

			SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
			", 
			{
				referralID = { value=arguments.referralID, cfsqltype="CF_SQL_INTEGER" }
			},
			{ datasource="membercentral" }
		);

		return qryStatus;
	}

	private query function getClientPaymentData (required numeric orgID, required numeric referralID, required numeric clientID) {
		var qryClientPaymentData = queryExecute("
			SET NOCOUNT ON;
			SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

			declare @clientID int, @orgID int = :orgID, @referralID int = :referralID;

			select @clientID = coalesce(clientParentID, clientID)
			from dbo.ref_clients 
			where clientID = :clientID;

			WITH ordTrans as (
				select ts.transactionID as saleTID, ts.transactionID, ts.cache_amountAfterAdjustment, 
					ts.cache_amountAfterAdjustment-ts.cache_activePaymentAllocatedAmount-ts.cache_pendingPaymentAllocatedAmount as amtToBePaid
				from dbo.tr_applications as tra
				inner join dbo.tr_transactionSales as ts on ts.orgID = @orgID
					and ts.transactionID = tra.transactionID
				where tra.orgID = @orgID
				and tra.itemType = 'ClientReferralFee'
				and tra.itemID = @clientID
				and tra.status = 'A'
					union all
				select rt.transactionID as saleTID, t.transactionID, ts.cache_amountAfterAdjustment, 
					ts.cache_amountAfterAdjustment-ts.cache_activePaymentAllocatedAmount-ts.cache_pendingPaymentAllocatedAmount as amtToBePaid
				from dbo.tr_transactions as t
				inner join dbo.tr_relationships as tr on tr.orgID = @orgID and tr.typeID = 4 and tr.transactionID = t.transactionID
				inner join dbo.tr_transactionSales as ts on ts.orgID = @orgID and ts.transactionID = t.transactionID
				inner join ordTrans as rt on rt.transactionID = tr.appliedToTransactionID 
				where t.ownedByOrgID = @orgID 
				and t.typeID = 7
			)
			select distinct
				ot.saleTID, ot.clientReferralDues, ot.amtToBePaid, (ot.clientReferralDues - ot.amtToBePaid) as paidToDate,
				ts.saleID, ts.transactionID, ts.cache_amountAfterAdjustment,
				ts.cache_activePaymentAllocatedAmount, ts.cache_pendingPaymentAllocatedAmount,
				ts.stateIDForTax, ts.zipForTax, tra.itemType,
				t.ownedByOrgID, t.recordedOnSiteID, t.statusID,
				t.detail, t.parentTransactionID, t.amount,
				t.dateRecorded, t.transactionDate, t.assignedToMemberID,
				t.recordedByMemberID, t.statsSessionID, t.typeID, t.debitGLAccountID, t.creditGLAccountID,
				(m.firstName  + 
				case
					when m.middlename is not null and len(m.middlename) > 0  then
						' ' + left(m.middleName, 1) + ' '
					else
						' '
				end  + m.lastName) as recordedBy
			from dbo.tr_applications tra
			inner join dbo.tr_transactionSales ts on ts.orgID = @orgID and ts.transactionID = tra.transactionID 
			inner join dbo.tr_transactions t on t.ownedByOrgID = @orgID and t.transactionID = ts.transactionID
			inner join (
				select saleTID, sum(cache_amountAfterAdjustment) as clientReferralDues, sum(amtToBePaid) as amtToBePaid
				from ordTrans
				group by saleTID
			) ot on ot.saleTID = tra.transactionID
			inner join dbo.ams_members m on m.memberID = t.recordedByMemberID
			where tra.orgID = @orgID 
			and ts.cache_amountAfterAdjustment >= 0;

			SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
			", 
			{
				orgID = { value=arguments.orgID, cfsqltype="CF_SQL_INTEGER" },
				referralID = { value=arguments.referralID, cfsqltype="CF_SQL_INTEGER" },
				clientID = { value=arguments.clientID, cfsqltype="CF_SQL_INTEGER" }
			},
			{ datasource="membercentral" }
		);

		return qryClientPaymentData;
	}

	private void function removeClientPaymentInfo (required numeric orgID, required numeric referralID, required numeric clientID, required numeric recordedbymemberid) {
		var qryRemoveClientPaymentInfo = queryExecute("
			SET XACT_ABORT, NOCOUNT ON;
			BEGIN TRY

				IF OBJECT_ID('tempdb..##tmpSaleTransIDs') IS NOT NULL
					DROP TABLE ##tmpSaleTransIDs;
				IF OBJECT_ID('tempdb..##tmpAllocPayments') IS NOT NULL
					DROP TABLE ##tmpAllocPayments;
				CREATE TABLE ##tmpSaleTransIDs (transactionID int);
				CREATE TABLE ##tmpAllocPayments (saleTransactionID int, paymentTransactionID int, allocatedAmount decimal(18,2));

				DECLARE @orgID int, @clientID int, @referralID int, @queueStatusID int, @recordedByMemberID int, @itemGroupUID uniqueidentifier = NEWID();
				SET @orgID = :orgID;
				SET @clientID = :clientID;
				SET @referralID = :referralID;
				SET @recordedByMemberID = :recordedbymemberid;
				EXEC platformQueue.dbo.queue_getStatusIDbyType @queueType='deleteCardsOnFile', @queueStatus='readyToProcess', @queueStatusID=@queueStatusID OUTPUT;

				INSERT INTO ##tmpSaleTransIDs (transactionID)
				SELECT DISTINCT tra.transactionID
				FROM dbo.tr_applications AS tra
				INNER JOIN dbo.ref_clients AS c ON c.referralID = @referralID
					AND c.clientID = tra.itemID
				WHERE tra.itemType = 'ClientReferralFee'
				AND tra.orgID = @orgID
				AND tra.status = 'A'
				AND c.clientID = @clientID;

				INSERT INTO ##tmpAllocPayments (saleTransactionID, paymentTransactionID, allocatedAmount)
				EXEC dbo.tr_getAllocatedPaymentsofSale @orgID=@orgID, @limitToSaleTransactionID=NULL;

				INSERT INTO platformQueue.dbo.queue_deleteCardsOnFile (itemGroupUID, payProfileID, profileID, memberID, customerProfileID, 
					paymentProfileID, recordedByMemberID, statusID, dateAdded, dateUpdated)
				SELECT DISTINCT @itemGroupUID, mpp.payProfileID, mpp.profileID, mpp.memberID, mpp.customerProfileID, mpp.paymentProfileID,
					@recordedByMemberID, @queueStatusID, GETDATE(), GETDATE()
				FROM dbo.tr_applications as tra
				INNER JOIN ##tmpAllocPayments as tmp on tmp.saleTransactionID = tra.transactionID
				INNER JOIN dbo.ref_clients as c on c.referralID = @referralID
					AND c.clientID = tra.itemID
				INNER JOIN dbo.tr_transactions as t on t.ownedByOrgID = @orgID 
					AND t.transactionID = tmp.paymentTransactionID
				INNER JOIN dbo.tr_transactionPayments as tp on tp.orgID = @orgID 
					AND tp.transactionID = t.transactionID
				INNER JOIN dbo.tr_paymentHistory as ph on ph.orgID = @orgID 
					AND ph.historyID = tp.historyID
				INNER JOIN dbo.ams_memberPaymentProfiles as mpp on mpp.profileID = tp.profileID 
				WHERE tra.itemType = 'ClientReferralFee'
				AND tra.status = 'A'
				AND tra.orgID = @orgID
				AND c.clientID = @clientID
				AND mpp.memberID = t.assignedToMemberID
				AND mpp.status = 'A';

				IF @@ROWCOUNT > 0
					EXEC dbo.sched_resumeTask @name='Delete Cards on File', @engine='MCLuceeLinux';

				IF OBJECT_ID('tempdb..##tmpSaleTransIDs') IS NOT NULL
					DROP TABLE ##tmpSaleTransIDs;
				IF OBJECT_ID('tempdb..##tmpAllocPayments') IS NOT NULL
					DROP TABLE ##tmpAllocPayments;

			END TRY
			BEGIN CATCH
				IF @@trancount > 0 ROLLBACK TRANSACTION;
				EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
			END CATCH
			", 
			{
				orgID = { value=arguments.orgID, cfsqltype="CF_SQL_INTEGER" },
				referralID = { value=arguments.referralID, cfsqltype="CF_SQL_INTEGER" },
				clientID = { value=arguments.clientID, cfsqltype="CF_SQL_INTEGER" },
				recordedbymemberid = { value=arguments.recordedbymemberid, cfsqltype="CF_SQL_INTEGER" }
			},
			{ datasource="membercentral" }
		);
	}

	private void function adjustClientReferralFeeToZero (required numeric orgID, required numeric siteID, required query qryClientPaymentData, required numeric recordedbymemberid) {
		var qryAdjustClientPayments = new Query(
			sql= "SELECT * FROM qryItems WHERE clientReferralDues > 0",
			dbtype= "query",
			qryItems = arguments.qryClientPaymentData
		).execute().getResult();

		if (qryAdjustClientPayments.recordCount) {
			var adjustFeesSQL = "";
			for (var thisFee in qryAdjustClientPayments) {
				savecontent variable="adjustFeesSQL" {
					writeOutput('#adjustFeesSQL# 
						select @assignedToMemberID=NULL, @saleTID=NULL, @invoiceProfileID=NULL, @invoiceID=NULL, @oldFeeNeg=NULL;
						set @assignedToMemberID = #val(thisFee.assignedToMemberID)#;
						set @saleTID = #val(thisFee.saleTID)#;
						set @oldFeeNeg = #val(thisFee.clientReferralDues)# * -1;

						select @invoiceProfileID = gl.invoiceProfileID
						from dbo.tr_glAccounts as gl
						inner join dbo.tr_transactions as t on t.ownedByOrgID = @orgID 
							and t.creditGLAccountID = gl.glAccountID 
							and t.transactionID = @saleTID
						where gl.orgID = @orgID;

						select @invoiceID = invoiceID from @tblInvoices where invoiceProfileID = @invoiceProfileID;

						IF @invoiceID is null BEGIN
							EXEC dbo.tr_createInvoice @invoiceProfileID=@invoiceProfileID, @enteredByMemberID=@recordedByMemberID, 
								@assignedToMemberID=@assignedToMemberID, @dateBilled=@nowDate, @dateDue=@nowDate, 
								@invoiceID=@invoiceID OUTPUT, @invoiceNumber=@invoiceNumber OUTPUT;

							insert into @tblInvoices (invoiceID, invoiceProfileID)
							values (@invoiceID, @invoiceProfileID);
						END
					
						EXEC dbo.tr_createTransaction_adjustment @recordedOnSiteID=@siteID, @recordedByMemberID=@recordedByMemberID, 
							@statsSessionID=NULL, @amount=@oldFeeNeg, @taxAmount=NULL, @transactionDate=@nowDate, 
							@autoAdjustTransactionDate=1, @saleTransactionID=@saleTID, @invoiceID=@invoiceID, @byPassTax=0, 
							@byPassAccrual=0, @xmlSchedule=NULL, @transactionID=@adjTransactionID OUTPUT;

						UPDATE dbo.tr_applications
						SET [status] = ''D''
						WHERE transactionID = @saleTID
						AND orgID = @orgID;
					');
				};
			}
		
			var qryAdjClientRefFees = queryExecute("
				SET XACT_ABORT, NOCOUNT ON;
				BEGIN TRY

					DECLARE @siteID int, @orgID int, @recordedByMemberID int, @assignedToMemberID int, @oldFeeNeg decimal(18,2), 
						@saleTID int, @nowDate datetime, @invoiceProfileID int, @invoiceID int, @invoiceNumber varchar(18), @adjTransactionID int, 
						@invoiceIDList varchar(max);
					DECLARE @tblInvoices TABLE (invoiceID int, invoiceProfileID int);

					SET @siteID = :siteID;
					SET @orgID = :orgID;
					SET @recordedByMemberID = :recordedbymemberid;
					SET @nowDate = GETDATE();

					BEGIN TRAN;
						#adjustFeesSQL#

						select @invoiceIDList = COALESCE(@invoiceIDList+',','') + cast(invoiceID as varchar(10)) from @tblInvoices;
						IF len(@invoiceIDList) > 0
							EXEC dbo.tr_closeInvoice @orgID=@orgID, @enteredByMemberID=@recordedByMemberID, @invoiceIDList=@invoiceIDList;
					COMMIT TRAN;

				END TRY
				BEGIN CATCH
					IF @@trancount > 0 ROLLBACK TRANSACTION;
					EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
				END CATCH
				", 
				{
					orgID = { value=arguments.orgID, cfsqltype="CF_SQL_INTEGER" },
					siteID = { value=arguments.siteID, cfsqltype="CF_SQL_INTEGER" },
					recordedbymemberid = { value=arguments.recordedbymemberid, cfsqltype="CF_SQL_INTEGER" }
				},
				{ datasource="membercentral" }
			);
		}
	}

	private void function addReferralUpdateHistory (required numeric orgID, required numeric actorMemberID, required numeric clientReferralID, required string mainMessage, required array changes) {
		var strRequest = {
			"c": "historyEntries_SYS_ADMIN_REFERRALUPDATE",
			"d": {
				"HISTORYCODE": "SYS_ADMIN_REFERRALUPDATE",
				"ORGID": arguments.orgID,
				"ACTORMEMBERID": arguments.actorMemberID,
				"CLIENTREFERRALID": arguments.clientReferralID,
				"MAINMESSAGE": arguments.mainMessage,
				"CHANGES": arguments.changes
			}
		};

		var qryAddReferralHistory = queryExecute("
			INSERT INTO platformQueue.dbo.queue_mongo (msgjson) 
			VALUES (:msgjson);
			", 
			{ msgjson = { value=serializeJSON(strRequest), cfsqltype="CF_SQL_LONGVARCHAR" } },
			{ datasource="membercentral" }
		);
	}

}